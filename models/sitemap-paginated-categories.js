const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const sitemapPaginatedCategories = new mongoose.Schema({
  categoryId: { type: Number, required: true },
  pageNo: { type: Number, required: true },
  startId: { type: Number },
  totalPages: { type: Number },
  updatedAt: { type: Date, default: Date.now }
});

sitemapPaginatedCategories.index({
  categoryId: 1,
  pageNo: 1,
});

// Export schema
let conn = connectionLib.getPersonalityDatabaseConnection() || mongoose;
module.exports = conn.model('SitemapPaginatedCategories', sitemapPaginatedCategories);
