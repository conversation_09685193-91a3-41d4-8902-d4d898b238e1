const BannedUser = require('../../models/banned-user');
const BannedFile = require('../../models/banned-file');
const { deleteS3ObjectsInBulk } = require('../../lib/s3');
const { deleteFacesById, UPDATED_BANNED_USERS_COLL_ID } = require('../../lib/rekognition');

const USER_BATCH_SIZE = 500;
let cleanupInProgress = false;

const deleteFacesInBatches = async (faceIds, collectionId) => {
  const MAX_DELETE_FACES = 1000;
  for (let i = 0; i < faceIds.length; i += MAX_DELETE_FACES) {
    const batch = faceIds.slice(i, i + MAX_DELETE_FACES);
    await deleteFacesById(batch, collectionId);
  }
};

const processUserBatch = async (batch) => {
  const s3KeySet = new Set();
  const faceIdSet = new Set();
  const userSet = new Set();
  const recordIds = [];

  for (const record of batch) {
    if (record.user) userSet.add(record.user);
    record.userData?.pictures?.forEach((key) => s3KeySet.add(key));

    for (const id of record.faceIds || []) faceIdSet.add(id);
    recordIds.push(record._id);
  }

  if (s3KeySet.size > 0) await deleteS3ObjectsInBulk(Array.from(s3KeySet));
  if (faceIdSet.size > 0) await deleteFacesInBatches(Array.from(faceIdSet), UPDATED_BANNED_USERS_COLL_ID);
  if (userSet.size > 0) await BannedFile.deleteMany({ user: { $in: Array.from(userSet) } });
  if (recordIds.length > 0) await BannedUser.deleteMany({ _id: { $in: recordIds } });
};

const cleanupDeletedUsers = async (req, res, next) => {
  if (!process.env.TESTING) {
    res.json({});
  }

  if (cleanupInProgress) {
    console.log('[DeletedUsersCleanup][Info]', 'Cleanup job already running, skipping');
    return;
  }

  cleanupInProgress = true;
  const startTime = Date.now();

  try {
    console.log('[DeletedUsersCleanup][Info]', 'Cleanup job started');

    const cutoffDate = new Date();
    cutoffDate.setFullYear(cutoffDate.getFullYear() - 3);

    const total = await BannedUser.countDocuments({ deletedAt: { $lte: cutoffDate } });
    console.log(`[DeletedUsersCleanup][Info] Total users to process: ${total}`);

    let hasMore = true;
    let processedCount = 0;

    while (hasMore) {
      const users = await BannedUser.find({ deletedAt: { $lte: cutoffDate } })
        .sort({ deletedAt: 1 })
        .limit(USER_BATCH_SIZE)
        .select('_id user userData.pictures faceIds')
        .lean();

      if (users.length === 0) {
        hasMore = false;
        break;
      }

      await processUserBatch(users);
      processedCount += users.length;
      console.log(`[DeletedUsersCleanup][Info] Processed ${processedCount}/${total} users`);
    }

    const elapsed = Date.now() - startTime;
    console.log(`[DeletedUsersCleanup][Info] Cleanup completed in ${elapsed}ms`);
  } catch (error) {
    console.log('[DeletedUsersCleanup][Error]', error);
  } finally {
    cleanupInProgress = false;
    if (process.env.TESTING) {
      res.json({});
    }
  }
};

module.exports = { cleanupDeletedUsers };
