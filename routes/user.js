const mongoose = require('mongoose');
const express = require('express');
const router = express.Router();
const asyncHandler = require('express-async-handler');
const moment = require('moment');
const _ = require('underscore');
const haversine = require('haversine');
const { DateTime } = require('luxon');
const ct = require('countries-and-timezones');
const momentTz = require('moment-timezone');
const cmp = require('semver-compare');
const {
  notFoundError, forbiddenError, badRequestError, conflictError, applicationError, invalidInputError, requestTimeoutError,
} = require('../lib/http-errors');
const User = require('../models/user');
const HideList = require('../models/hide-list');
const HideOnSocial = require('../models/hide-on-social');
const UserMetadata = require('../models/user-metadata');
const ExclusionList = require('../models/exclusion-list');
const UsersWhoLiked = require('../models/users-who-liked');
const Chat = require('../models/chat');
const FriendList = require('../models/friend-list');
const ProfileView = require('../models/profile-view');
const Gift = require('../models/gift');
const Follow = require('../models/follow');
const BoostMetric = require('../models/boost-metric');
const Interest = require('../models/interest');
const LocationStat = require('../models/location-stat');
const Story = require('../models/story');
const UnBlocked = require('../models/unblocked');
const BanAppeal = require('../models/ban-appeal');
const ProfileTempBanAppeal = require('../models/profile-temp-ban-appeal');
const s3 = require('../lib/s3');
const emailLib = require('../lib/email');
const { sendFlashSaleEmail, sendEmailTemplate, sendEmailTemplateSES, getCommonTranslatedTags } = require('../lib/email');
const { findBannedFace } = require('../lib/rekognition');
const { convertHls } = require('../lib/mediaconvert');
const { isVideoInappropriate } = require('../lib/video-moderation');
const { handleSearch } = require('../lib/handle-search');
const admin = require('../config/firebase-admin');
const userMiddleware = require('../middleware/user');
const chatMiddleware = require('../middleware/chat');
const { specificRouteRateLimiter } = require('../middleware/user-rate-limiter');
const { verifyRecaptcha, verifyTurnstile } = require('../middleware/recaptcha');
const RecaptchaLog = require('../models/recaptchaLog');
const pricingConfigLib = require('../lib/pricing-config');
const locationStatLib = require('../lib/location-stat');
const metrosLib = require('../lib/metros');
const InterestCountryCount = require('../models/interest-country-count');
const { getNicheFromCampaign } = require('../lib/niche-campaigns');
const { getProfilesWithStories } = require('../lib/story');
const { getOnboardingInterestsEvents } = require('../lib/interest');
const { DAILY_PROFILE_ROUTE_LIMIT, DAILY_PROFILE_BLOCK_SESSION_TIME_FOR_LIMIT, DAILY_PROFILE_BLOCK_TIME_AFTER_LIMIT } = require('../lib/constants').API_RATE_LIMIT

const {
  createUserIfNotExist, findUser, findUserMetadata, findOtherUser, verifyNotBlocked, verifyPictureLimit, verifyPictureExists, deprecated, updateSignInInfo, validateEmailPhoneNumbers, validUserTranslation, updateAppMessage,
} = userMiddleware;
const { findPendingChat, findPendingChatPopulate, findChat } = require('../middleware/chat');
const actionLib = require('../lib/action');
const profilesLib = require('../lib/profiles-v3');
const abTest = require('../lib/ab-test');
const { updateUserScore } = require('../lib/score');
const reportLib = require('../lib/report');
const promptsLib = require('../lib/prompts');
const telepathyLib = require('../lib/telepathy');
const personalityLib = require('../lib/personality');
const userLib = require('../lib/user');
const { limitLikes, getRemainingDailyLimit } = require('../lib/user');
const socketLib = require('../lib/socket');
const Message = require('../models/message');
const Action = require('../models/action');
const Block = require('../models/block');
const DeleteAccountAttempt = require('../models/delete-account-attempt');
const PersonalityQuizResult = require('../models/personality-quiz-result');
const EnneagramQuizResult = require('../models/enneagram-quiz-result');
const HandleSearch = require('../models/handle-search');
const PromoCode = require('../models/promo-code');
const PurchasePremiumResponse = require('../models/purchase-premium-response');
const { iap, processPurchaseData } = require('../lib/iap');
const premiumLib = require('../lib/premium');
const { isPremium, getPreferences, isInterplanetary } = require('../lib/premium');
const constants = require('../lib/constants');
const chatLib = require('../lib/chat');
const { getProfileViews } = require('../lib/views');
const { pageSize, getKarmaTiers } = require('../lib/constants');
const notificationLib = require('../lib/notification');
const coinsLib = require('../lib/coins');
const socialLib = require('../lib/social');
const genderPreferenceLib = require('../lib/gender-preference');
const {
  addToExclusionList, addToTopProfilesExclusionList, blockUser, blockUsersWithDeviceId, unHideUsers, hideUsers,
} = require('../lib/action');
const coinsConstants = require('../lib/coins-constants');
const PurchaseReceipt = require('../models/purchase-receipt');
const CoinPurchaseReceipt = require('../models/coin-purchase-receipt');
const SuperLikePurchaseReceipt = require('../models/super-like-purchase-receipt');
const NeuronPurchaseReceipt = require('../models/neuron-purchase-receipt');
const StripeReceipt = require('../models/stripe-receipt');
const locationLib = require('../lib/location');
const { isLocal } = require('../lib/location');
const interestLib = require('../lib/interest');
const { translate, translate_frontend } = require('../lib/translate');
const { enneagrams } = require('../lib/enneagram');
const { horoscopes } = require('../lib/horoscope');
const { findBannedAccountKeywords, removeBannedKeywords } = require('../lib/report-constants');
const { getUserQuestions, getUserComments, getUserAnonymousQuestions, getUserAnonymousComments } = require('../lib/social');
const { languageCodes } = require('../lib/languages');
const countryjs = require('countryjs');
const countryCodeLookup = require('country-code-lookup');
const mailchimpLib = require('../lib/mailchimp');
const basic = require('../lib/basic');
const { hasDuplicates, removeDuplicates, arrayDiff } = require('../lib/basic');
const { sendMessageNotifications, saveMessage } = require('../lib/message');
const { checkForDeleteInstantMatch } = require('../lib/chat');
const { getUidsByNumberMail } = require('../config/firebase-admin');
const { betweenPunctuations } = require('../lib/text-search');
const { moreAboutUserChoices, relationshipStatusChoices, datingSubPreferencesChoices, relationshipTypeChoices, sexualityChoices } = require('../lib/moreAboutUser');
const { onPromptsUpdated, onInterestsUpdated } = require('../lib/coins');
const { getAge } = require('../lib/horoscope');
const { postTranslation } = require('../lib/translation');
const { scammerImageDetection } = require('../lib/scammer-image-detection');
const { getBlockLookup, profilePreviewProjection } = require('../lib/projections');
const countryLib = require('../lib/country');
const { sendSocketEvent } = require('../lib/socket');
const { addProfilePicture, removeProfilePicture } = require('../lib/liveness');
const { verifyProfilePicture, setFaceComparisonReferenceImage } = require('../lib/verification');
const openai = require('../lib/openai');
const { lambda } = require('../lib/lambda');
const PoseVerification = require('../models/pose-verification');
const { spamHandles } = require('../lib/spam-handles');
const {getStripeSubscriptionByCustomer} = require('../lib/purchase')
const {conversionEmitter} = require('../lib/conversion')
const DataRequestHistory = require('../models/data-request-history');
const { isValidGif } = require('../lib/gif');
const { transcribeAudio } = require('../lib/deepgram');
const ExperimentRecording = require('../lib/experiment-recording')
const { ImageModerationService } = require('../lib/image-moderation');
const YotiVerification = require('../models/yoti-verification');
const { getYotiDetectionResult } = require('../lib/yoti-client');
const UserEmbeddings = require('../models/user-embeddings');
const TranslationRating = require('../models/translation-rating');
const { findSpamInImage } = require('../lib/ocr-moderation');
const BannedFile = require('../models/banned-file');
const SuperLikeTransaction = require('../models/super-like-transaction');
const {appsflyerToKochava} = require('../lib/appsflyer')
const UsersAiTailoredPrompts = require('../models/users-ai-tailored-prompts');
const { updateBatchStatus } = require("../lib/ai-image/imagine");
const { batchStatus } = require("../lib/ai-image/const")

async function checkForSpamHandles(user, text) {
  keyword = spamHandles.find((x) => new RegExp(`\\b${x}\\b`).test(text.toLowerCase()));
  if (keyword) {
    await reportLib.autoShadowBan(user, 'spam handle', keyword);
  }
}

function getTranslationConfigLanguage(user) {
  /*
  const languages = [ 'ja', 'ko', 'ms' ];
  for (const language of languages) {
    const config = `app_431_${language}`;
    if (user.config[config] !== undefined) {
      return language;
    }
  }
  */
  return null;
}

function showTopProfiles(user) {
  if (!user.versionAtLeast('1.11.7')) {
    return false;
  }
  return true;
}


async function onLikeSentPreResponse(user, otherUser) {

  // add to exclusion list
  {
    const start = new Date().getTime();
    await addToExclusionList(user._id, otherUser._id);

    const end = new Date().getTime();
    console.log(`User ${user._id} send like, time to update my exclusion list: ${end-start} ms`);
  }

  // save profile view
  await onProfileView(user, otherUser, false);

  // save the like
  let action;
  {
    const start = new Date().getTime();

    action = await Action.findOne({ from: user._id, to: otherUser._id });
    const threeMonthsAgo = new Date(Date.now() - 3 * 30 * 24 * 60 * 60 * 1000) // 3 months before
    if (action && action.createdAt && action.createdAt < threeMonthsAgo) {
      action.createdAt = new Date();
    }
    if (!action) {
      action = new Action({
        from: user._id,
        to: otherUser._id,
      });
    }
    action.like = true;
    await action.save();

    const end = new Date().getTime();
    console.log(`User ${user._id} send like, time to save action: ${end-start} ms`);
  }

  return action;
}

async function onNewPendingLike(user, otherUser, chat) {

  // updateMetrics for other user
  {
    const start = new Date().getTime();
    const isChatVisible = chatLib.isChatVisible(chat, otherUser);
    await otherUser.resetCurrentDayMetricsIfNeeded();
    {
      const metricsToIncrement = [
        'numLikesReceived',
        'numPendingLikes',
        'numActionsReceived',
        // legacy
        'numLikesReceivedCurrentDay',
      ];
      if (isChatVisible) {
        metricsToIncrement.push('numLikesReceivedVisible');
      }
      if(otherUser.isBoostActive()){
        await User.incrementMetric(otherUser._id,'numActionsReceivedDuringBoostQuota',-1)
      }
      await User.incrementMetrics(otherUser._id, metricsToIncrement);
    }
    {
      const metricsToIncrement = [
        'numMinutesUntilFirstLikeReceived',
      ];
      if (isChatVisible) {
        metricsToIncrement.push('numMinutesUntilFirstLikeReceivedVisible');
      }
      await otherUser.updateNumMinutesUntilParams(metricsToIncrement);
    }
    await updateUserScore(otherUser, { actions: 1 });

    if (otherUser.isBoostActive()) {
      const metrics = ['numLikesReceived', 'numActionsReceived'];
      const local = isLocal(user, otherUser);
      if (local) {
        metrics.push('numLocalLikesReceived');
        metrics.push('numLocalActionsReceived');
      }
      await BoostMetric.incrementMetrics(otherUser._id, metrics);
    }
    else if (otherUser.isPostBoostPeriod()) {
      const metrics = ['postBoostNumActionsReceived', 'postBoostNumLikesReceived'];
      await BoostMetric.incrementMetrics(otherUser._id, metrics);
    }

    const end = new Date().getTime();
    console.log(`User ${user._id} send like, time to update metrics for other user: ${end-start} ms`);
  }

  const tags = profilesLib.getProfileTags(user,otherUser)
  await User.incrementMetrics(user._id, profilesLib.getMetricTags(tags, true));
}

async function onProfileView(user, otherUser, notify, source) {
  if (!otherUser) {
    return;
  }
  const otherUserId = otherUser._id;
  const doc = await ProfileView.findOne({ from: user._id, to: otherUserId });
  if (doc) {
    return;
  }

  try {
    await ProfileView.create({ from: user._id, to: otherUserId, source });
    await User.incrementMetrics(otherUserId, [
      'numProfileViews',
    ]);

    if (notify && !isPremium(otherUser)) {
      const createdAtNumDays = moment().diff(otherUser.createdAt, 'days');
      const notifiedAtNumDays = otherUser.metrics.profileViewNotifiedAt ? moment().diff(otherUser.metrics.profileViewNotifiedAt, 'days') : Infinity;

      if ((createdAtNumDays < 7 && notifiedAtNumDays >= 1) || (createdAtNumDays >= 7 && notifiedAtNumDays >= 7)) {

        let analyticsLabel = 'someone-viewed-your-profile';
        let notificationTitle = 'Someone viewed your profile 👀'
        let notificationBody = 'See who.'

        admin.sendNotification(
          otherUser,
          'promotions',
          notificationTitle,
          notificationBody,
          { premiumPopup: 'seeWhoViewed' },
          null,
          'love',
          analyticsLabel,
          true,
        );
        otherUser.metrics.profileViewNotifiedAt = Date.now();
        await otherUser.save();
      }
    }
  } catch (err) {
    // profile view already exists
  }
}

async function checkForUpdateToNewReverification(user) {
  if (user.verification.status == 'verified' && !user.verification.newReverificationSystem && userLib.useNewReverification(user)) {
    user.verification.newReverificationSystem = true;
    if (user.verification.status === 'verified') {
      user.verification.reVerification = true;
    }
    const result = await verifyProfilePicture(user);
    if (result == 'manual') {
      await user.setVerificationStatus('reverifying');
    } else if (result == 'reject') {
      await user.setVerificationStatus('rejected');
      user.verification.rejectionReason = 'Make sure your first profile picture is a picture of you, and only you.';
    }
  }
}

function shouldNewReverify(user) {
  return userLib.useNewReverification(user) && (user.verification.status == 'verified' || (user.verification.status == 'rejected' && user.verification.rejectionReason == 'Make sure your first profile picture is a picture of you, and only you.'));
}

async function handleApprovedProfilePicture(user, key, res) {
  user.initOriginalPictures();
  user.originalPictures.push(key);
  await reportLib.computePictures(user);
  await checkForUpdateToNewReverification(user);
  if (user.verification.status == 'verified' && !userLib.useNewReverification(user)) {
    if (user.livenessVerification?.compareFacesSuccess || user.livenessVerification?.manuallyCheckedResult) {
      const addResult = await addProfilePicture(user, user.livenessVerification, key);
      if (addResult == 'does not match') {
        await user.setVerificationStatus('reverifying');
      }
    } else {
      await user.setVerificationStatus('reverifying');
    }
  }
  user.calculateViewableInDailyProfiles();
  await user.save();

  /*
  if (user.pictures.length == 1 && user.birthday) {
    let createdAt = DateTime.fromJSDate(user.createdAt, { zone: 'utc' });
    if (user.timezone) {
      createdAt = createdAt.setZone(user.timezone);
    }
    const birthday = DateTime.fromJSDate(user.birthday, { zone: 'utc' });
    if (createdAt.month == birthday.month && createdAt.day == birthday.day) {
      await reportLib.createReport(
        user,
        null,
        ['Auto-report due to birthday == createdAt'],
        `birthday: ${user.birthday.toISOString()}`,
      );
    }
  }
  */

  updateUserScore(user, { pictures: 1 });

  const coinReward = await coinsLib.onPicturesUpdated(user);
  socketLib.sendCoinRewards(user._id, coinReward);

  res.json({
    pictures: user.originalPictures,
    coinReward: coinReward || undefined,
  });
}

async function approveChat(chat, user, otherUser, io) {
  // update chat
  chat.pendingUser = null;
  chat.lastMessageTime = Date.now();
  const readReceipt = chat.readReceipts?.get(user._id);
  if (readReceipt) {
    readReceipt.numUnreadMessages = 0;
    if (premiumLib.isPremium(user) && !readReceipt.matchIndicator) {
      readReceipt.matchIndicator = 'infinity';
    }
    chat.readReceipts.set(user._id, readReceipt);
  } else {
    chat.readReceipts.set(user._id, {
      numUnreadMessages: 0,
      matchIndicator: premiumLib.isPremium(user) ? 'infinity' : undefined,
    });
  }

  chatLib.incrementUnreadMessages(chat, otherUser._id);

  // handling 'boost' indicator
  if (otherUser.isBoostActive()) {
    const otherUserReadReceipt = chat.readReceipts?.get(otherUser._id);
    if (otherUserReadReceipt) {
      otherUserReadReceipt.matchIndicator = 'boost';
      chat.readReceipts.set(otherUser._id, otherUserReadReceipt);
    } else {
      chat.readReceipts.set(otherUser._id, { numUnreadMessages: 1, matchIndicator: 'boost' });
    }
  }
  const savedChat = await chat.save();

  // save the like
  let action = await Action.findOne({ from: user._id, to: otherUser._id });
  if (!action) {
    action = new Action({
      from: user._id,
      to: otherUser._id,
    });
  }
  action.like = true;
  await action.save();

  // updateMetrics for this user
  if (user.metrics.numMatches == 0) {
    user.metrics.numActionsSentBeforeFirstMatch = user.metrics.numActionsSent;
    user.metrics.numLikesSentBeforeFirstMatch = user.metrics.numLikesSent;
    await user.save();
  }
  await user.updateNumMinutesUntilParams('numMinutesUntilFirstMatch');
  await User.incrementMetrics(user._id, [
    'numApprovalsSent',
    'numMatches',
  ]);
  await User.incrementMetric(user._id, 'numPendingLikes', -1);

  // updateMetrics for other user
  if (otherUser.metrics.numMatches == 0) {
    otherUser.metrics.numActionsSentBeforeFirstMatch = otherUser.metrics.numActionsSent;
    otherUser.metrics.numLikesSentBeforeFirstMatch = otherUser.metrics.numLikesSent;
    await otherUser.save();
  }
  await otherUser.updateNumMinutesUntilParams('numMinutesUntilFirstMatch');
  await User.incrementMetrics(otherUser._id, [
    'numApprovalsReceived',
    'numMatches',
  ]);

  if (!user.shadowBanned) {
    await Follow.autoFollowMatch(user, otherUser);
    await Follow.autoFollowMatch(otherUser, user);

    const formattedChat = chatLib.formatChat(
      savedChat.toObject({ flattenMaps: true }),
      otherUser,
    );

    sendSocketEvent(otherUser._id, 'approved chat', formattedChat);

    const data = {
      _id: savedChat._id,
    };
    admin.sendNotification(
      otherUser,
      'matches',
      translate('New match!', otherUser.locale),
      translate('%s likes you too!', otherUser.locale, user.firstName),
      { approvedChat: JSON.stringify(data) },
      null,
      'love',
      'new-match',
    );
  }

  // update friend list
  await FriendList.addFriendship(user._id, otherUser._id);
  // delete from unmatched list
  await chatLib.deleteUnmatchedChatRecord(chat);
}

function renameEthnicity(ethnicity) {
  if (ethnicity == 'Berber') {
    return 'Amazigh';
  }else if(ethnicity == 'Anglo-Irish'){
    return 'Irish';
  }
  return ethnicity;
}

function getBorders(countryCode) {
  if (countryCode == 'PS') {
    return ['ISR'];
  }
}

function removeHiddenProfileText(user, text) {
  if (!user.hiddenProfileText || user.hiddenProfileText.length == 0) {
    return text;
  }
  for (const hiddenText of user.hiddenProfileText) {
    text = text.replaceAll(hiddenText, '');
  }
  return text;
}

const autoBanUnderage = [];
for (const age of [12, 13, 14, 15, 16, 17]) {
  // english
  for (const s of ['i\'m', 'im', 'i am']) {
    autoBanUnderage.push(`${s} ${age}`);
  }

  // spanish
  autoBanUnderage.push(`tengo ${age} años`);

  // portuguese
  autoBanUnderage.push(`tehno ${age} anos`);

  // german
  autoBanUnderage.push(`ich bin ${age}`);
}

const autoBanTimezoneCountryPairs = [
  ['Asia/Manila', 'France'],
  ['Asia/Manila', 'United Kingdom'],
  ['Asia/Manila', 'Switzerland'],
  ['Asia/Manila', 'Poland'],
  ['Asia/Manila', 'Singapore'],
  ['Africa/Lagos', 'Germany'],
  ['Asia/Shanghai', 'France'],
  ['Asia/Phnom_Penh', 'France'],
  ['Asia/Yangon', 'United States'],
  ['Europe/Brussels', 'China'],
  ['Asia/Bangkok', 'Myanmar (Burma)'],
  ['Asia/Bangkok', 'Canada'],
  ['Asia/Vientiane', 'United States'],
  ['Asia/Manila', 'Canada'],
  ['America/Los_Angeles', 'Thailand'],
  ['Asia/Ho_Chi_Minh', 'United States'],
  ['Asia/Shanghai', 'Taiwan'],
  ['Africa/Lagos', 'United States'],
  ['Asia/Rangoon', 'United States'],
  ['US/Central', 'Nigeria'],
  ['US/Pacific', 'Cambodia'],
  ['America/New_York', 'Colombia'],
  ['America/Los_Angeles', 'China'],
  ['Africa/Tunis', 'Spain'],
  ['Asia/Phnom_Penh', 'United States'],
  ['US/Pacific', 'Nigeria'],
  ['Asia/Shanghai', 'Austria'],
  ['Africa/Brazzaville', 'United States'],
  ['Asia/Shanghai', 'Australia'],
  ['Asia/Taipei', 'Thailand'],
  ['Asia/Hong_Kong', 'United Kingdom'],
  ['Asia/Hong_Kong', 'Thailand'],
  ['Asia/Manila', 'New Zealand'],
  ['Asia/Jakarta', 'Singapore'],
  ['Asia/Manila', 'Ireland'],
  ['Australia/Sydney', 'Singapore'],
  ['Asia/Manila', 'United States'],
  ['Asia/Shanghai', 'United States'],
  ['Asia/Tokyo', 'Thailand'],
];

const autoBanTimezoneCountryList = [
  'Australia',
  'Thailand',
  'Bangkok',
];

async function checkTimezoneCountry(user) {
  let bannedReason = null;
  let bannedComment = null;

  /*
  function checkTimezoneCountryPair(x) {
    return x[0] == user.timezone && x[1] == user.actualCountry;
  }

  if (autoBanTimezoneCountryPairs.some(checkTimezoneCountryPair)) {
    bannedReason = 'timezone does not match country';
    bannedComment = `${user.timezone} != ${user.actualCountry}`;
  }
  else if (user.timezone && user.actualCountryCode) {
    const countries = ct.getCountriesForTimezone(user.timezone);
    if (!countries.some((c) => c.id == user.actualCountryCode)) {
      const bordersIso3 = countryjs.borders(user.actualCountryCode)
        || getBorders(user.actualCountryCode)
        || [];
      const borders = bordersIso3.map((x) => {
        const rv = countryCodeLookup.byIso(x);
        if (rv) {
          return rv.iso2;
        }
      });
      if (!countries.some((c) => borders.includes(c.id))) {
        for (const s of autoBanTimezoneCountryList) {
          if (user.timezone.includes(s) || user.actualCountry == s) {
            bannedReason = 'timezone does not match country';
            bannedComment = `${user.timezone} != ${user.actualCountry}`;
          }
        }
        if (!bannedReason) {
          await reportLib.createReport(
            user,
            null,
            ['Auto-report: timezone does not match country'],
            `${user.timezone} != ${user.actualCountry}`,
          );
        }
      }
    }
  }
  */

  // Nigeria, Ghana, Senegal, or Togo - ban due to scammers
  if (user.actualCountry == 'Nigeria' || user.timezone == 'Africa/Lagos') {
    bannedReason = 'Nigeria';
  } else if (user.actualCountry == 'Ghana' || user.timezone == 'Africa/Accra') {
    bannedReason = 'Ghana';
  } else if (user.actualCountry == 'Senegal' || user.timezone == 'Africa/Dakar') {
    bannedReason = 'Senegal';
  } else if (user.actualCountry == 'Togo' || user.timezone == 'Africa/Lome') {
    bannedReason = 'Togo';
  }

  if (bannedReason) {
    await reportLib.autoShadowBan(user, bannedReason, bannedComment);
  }

  if (user.actualCountry && user.actualCountry != 'China') {
    if (user.signupCountry == 'China' || user.timezone == 'Asia/Shanghai') {
      await reportLib.createReport(
        user,
        null,
        ['Auto-report: china timezone country mismatch'],
        `actualCountry: ${user.actualCountry}, signupCountry: ${user.signupCountry}, timezone: ${user.timezone}`,
      );
    }
  }
}

async function grantAudioDescriptionReward(user) {
  const userId = user._id;
  const coins = await coinsLib.updateCoins(
    { user: userId, audioDescriptionRewardReceived: { $ne: true } },
    {
      $inc: { coins: coinsConstants.audioDescriptionReward },
      $set: { audioDescriptionRewardReceived: true },
    },
    'Set Audio Description in Profile Reward',
  );
  if (coins) {
    const coinReward = [{
      caption: translate('Set Audio Description in Profile Reward', user.locale),
      rewardAmount: coinsConstants.audioDescriptionReward,
      newTotal: coins,
    }];
    socketLib.sendCoinRewards(userId, coinReward);
  }
}

async function grantEnneagramReward(user) {
  const userId = user._id;
  const coins = await coinsLib.updateCoins(
    { user: userId, enneagramRewardReceived: { $ne: true } },
    {
      $inc: { coins: coinsConstants.enneagramReward },
      $set: { enneagramRewardReceived: true },
    },
    'Set Enneagram in Profile Reward',
  );
  if (coins) {
    const coinReward = [{
      caption: translate('Set Enneagram in Profile Reward', user.locale),
      rewardAmount: coinsConstants.enneagramReward,
      newTotal: coins,
    }];
    socketLib.sendCoinRewards(userId, coinReward);
  }
}

async function grantQuizAnswerReward(user) {
  const userId = user._id;
  const coins = await coinsLib.updateCoins(
    { user: userId, quizAnswerRewardReceived: { $ne: true } },
    {
      $inc: { coins: coinsConstants.quizAnswerReward },
      $set: { quizAnswerRewardReceived: true },
    },
    'Complete Personality Quiz Reward',
  );

  if (coins) {
    const coinReward = [{
      caption: translate('Complete Personality Quiz Reward', user.locale),
      rewardAmount: coinsConstants.quizAnswerReward,
      newTotal: coins,
    }];
    socketLib.sendCoinRewards(userId, coinReward);
  }
}

async function getPurchaseInfo(user) {
  try {
    const purchaseReceiptPromise = PurchaseReceipt.findOne({ user: user._id }).sort({ purchaseDate: -1 });
    const stripeReceiptPromise = StripeReceipt.findOne({ user: user._id }).sort({ createdAt: -1 });

    const [purchaseReceipt, stripeReceipt] = await Promise.all([purchaseReceiptPromise, stripeReceiptPromise]);

    let stripeData = null
    let purchaseData = null
    let isManual = false

    if(stripeReceipt) {
      let status = 'active'
      if (stripeReceipt.product.includes('infinity_lifetime')) {
        // some lifetime receipts have an invalid expiration date, so hardcode the
        // return value for lifetime purchases
        return {
          status: status,
          productId: stripeReceipt.product,
        }
      }

      const markAsCancelled = ['incomplete', 'incomplete_expired', 'past_due', 'unpaid', 'canceled']
      const stripeSubscription = await getStripeSubscriptionByCustomer(stripeReceipt.stripeCustomerId)
      if(stripeSubscription) {

        if (stripeSubscription.current_period_end < Math.floor(Date.now() / 1000)){
          status = 'expired'
        }else if(markAsCancelled.includes(stripeSubscription.status)){
          status = 'cancelled'
        }

        stripeData = {
          status: status,
          productId: stripeSubscription.items.data[0].price.lookup_key,
          expirationDate: DateTime.fromSeconds(stripeSubscription.current_period_end).toUTC().toISO(),
        }

        if (new Date(user.premiumExpiration) > new Date(stripeSubscription.current_period_end * 1000)) isManual = true
      }
    }

    if (purchaseReceipt) {
      if (purchaseReceipt.productId.includes('infinity_lifetime')) {
        // some lifetime receipts have an invalid expiration date, so hardcode the
        // return value for lifetime purchases
        return {
          status: 'active',
          productId: purchaseReceipt.productId,
        }
      }

      let status = 'active';
      if (purchaseReceipt.expirationDate < new Date()) {
        status = 'expired';
      } else if (purchaseReceipt.revokedAt || purchaseReceipt.cancelledAt) {
        status = 'cancelled';
      }

      purchaseData = {
        status: status,
        productId: purchaseReceipt.productId,
        expirationDate: purchaseReceipt.expirationDate,
      }

      if (new Date(user.premiumExpiration) > new Date(purchaseReceipt.expirationDate)) isManual = true
    }

    if(user.versionAtLeast('1.13.69')){
      if(isManual || !stripeData && !purchaseData){
        //handle manually added subscription
        if(user.premiumExpiration && new Date(user.premiumExpiration) > new Date()){
          return {
            status: 'active',
            productId: null,
            expirationDate: user.premiumExpiration,
          }
        }else if(user.premiumExpiration){
          return {
            status: 'expired',
            productId: null,
            expirationDate: user.premiumExpiration,
          }
        }
      }
    }

    if (!stripeData) return purchaseData;
    if (!purchaseData) return stripeData;
    if (stripeData.status === 'active' && purchaseData.status !== 'active') return stripeData;
    if (stripeData.status !== 'active' && purchaseData.status === 'active') return purchaseData;
    return  new Date(stripeData.expirationDate) > new Date(purchaseData.expirationDate) ? stripeData : purchaseData;
  } catch (error) {
    console.log('getPurchaseInfo error: ', error)
    return null
  }
}

// numLikesReceivedDuringBoost as indicator of boost Pop up, APP-236
async function getNumLikesReceivedDuringBoost(user){
  if (user.versionAtLeast('1.13.53')){
    if((user.boostExpiration && user.boostExpiration < Date.now()) && user.postBoostPopupHandled !== true){
      const mostRecentBoost = await BoostMetric.findOne({ user: user._id }).sort(
        "-boostExpiration"
      );

      user.postBoostPopupHandled = true
      await user.save()
      if(mostRecentBoost.numLikesReceived >= 3){
        return mostRecentBoost.numLikesReceived
      }
    }
  }
  return undefined
}

// Generalized function to handle conditional automated messages
async function sendConditionalMessage(user, condition, triggerKey, value, messageTemplate, templateParams, additionalInfo) {
  // Early return if the condition is false
  if (!condition) return;

  const isMarkDownVersion = user.versionAtLeast('1.13.73');

  // Prepare additional data if exists
  let additionalData = additionalInfo ? {
    ...additionalInfo,
    replaceText: translate(additionalInfo.replaceText, user.locale),
  } : undefined;
  let replyText = translate(messageTemplate, user.locale, templateParams || {});

  // Handle additionalData if it exists
  if (additionalData) {
    const replaceTextLink = isMarkDownVersion
      ? `\n\n[${additionalData.replaceText}](/${additionalData.openPage})`
      : `\n\n${additionalData.replaceText}`;

    replyText += replaceTextLink;

    // Clear additionalData if the user's version is >= 1.13.73
    if (isMarkDownVersion) {
      additionalData = undefined;
    }
  }

  // Send automated reply
  await chatLib.sendAutomatedReply(user, replyText, true, additionalData);

  user.booMessages[triggerKey] = value;
  await user.save();
}

async function handleWhatsNewMessage(user) {
  const latestAppVersion = interestLib.whatsNewMessage?.Version;
  const lastMessagedVersion = user.booMessages?.whatsNewSentAtVersion;

  if (!latestAppVersion) {
    return; // No version to compare
  }

  if (lastMessagedVersion && cmp(latestAppVersion, lastMessagedVersion) <= 0) {
    return; // Message was sent for this version
  }

  if (user.signupAppVersion && cmp(latestAppVersion, user.signupAppVersion) <= 0) {
    return; // User signed up with latest version
  }

  let messageText = interestLib.whatsNewMessage[user.locale || 'en'] || interestLib.whatsNewMessage.en;
  let additionalInfo = {};

  if (cmp(latestAppVersion, user.appVersion) > 0) {
    // User needs to update the app
    additionalInfo = { replaceText: 'Update Now', openPage: 'appStoreLink' };
  }

  await sendConditionalMessage(
    user,
    true,
    'whatsNewSentAtVersion',
    latestAppVersion,
    messageText,
    undefined,
    Object.keys(additionalInfo).length ? additionalInfo : undefined,
  );
}

function prepareQuotedField(quoteFields){
  let quoteField = null
  let quoteValue = null
  for (const field in quoteFields) {
    if (quoteFields[field]) {
      quoteField = field;
      quoteValue = quoteFields[field];
      break // Exit the loop once a value is found
    }
  }
  return {quoteField, quoteValue}
}

const checkYotiVerificationEligibility = async (user) => {
  if (user?.verification?.status === 'verified') {
    return false;
  }

  const numPriorAttempts = await YotiVerification.countDocuments({
    user: user._id,
    createdAt: { $gt: moment().subtract(24, 'hours').toDate() },
  });
  if (numPriorAttempts >= 5) {
    return false;
  }

  return true;
};

const banUserIfFileIsAlreadyBanned = async (user, key) => {
  if (user.isVerified() || user.banned || user.shadowBanned) return;
  const imageHash = await s3.generateHashFromS3(key);
  if (imageHash) {
    const alreadyBanned = await BannedFile.find({ 'bannedImages.imageHash': imageHash });
    if (alreadyBanned.length > 0) {
      await reportLib.shadowBan(user, null, `banned file found in profile picture`, `banned file found in image: ${key}, previously banned user: ${alreadyBanned[0].user}`, { key, imageHash });
    }
  }
};

module.exports = function () {
  // Get all user information (create if not found)
  router.get(
    '/',
    asyncHandler(userMiddleware.createUserIfNotExist),
    asyncHandler(userMiddleware.findUserMetadata),
    userMiddleware.updateSignInInfo,
    userMiddleware.checkTeleportExpiration,
    asyncHandler(async (req, res, next) => {
      const rv = await userLib.formatMyProfile(req.user, req.userMetadata, req.ip);
      res.json(rv);
    }),
  );

  router.get('/personality', deprecated, (req, res, next) => next(notFoundError()));

  router.put('/firstName', findUser, asyncHandler(async (req, res, next) => {
    if (req.body.firstName == undefined) {
      return next(badRequestError());
    }
    if (!req.body.firstName) {
      return next(invalidInputError('Name cannot be blank'));
    }
    if (req.body.firstName.length > 50) {
      return next(invalidInputError('Character limit exceeded'));
    }
    const { user } = req;
    user.firstName = req.body.firstName;
    await user.save();

    await User.updateSearchFields(req.user._id);

    if (
      user.email
      && user.firstName
      && user.metrics.numTipEmailsSent == 0
      && !(user.pushNotificationSettings && (user.pushNotificationSettings.email === false))
      && user.locale == 'en'
      && !user.events.finished_signup
    ) {
      /*
      const discount = premiumLib.getPremiumFlashSale(user);
      if (discount) {
        if (user.isConfigTrue('web_flash_sale_email_v3')) {
          await sendFlashSaleEmail(user);
        }
        user.metrics.numFlashSaleEmailsSent = 1;
      }
      */
      await emailLib.sendWelcomeEmail(user);
      user.metrics.numTipEmailsSent = 1;
      await user.save();
      console.log(`Sent welcome email to ${user._id} ${user.email}`);
    }

    res.json({
      firstName: user.firstName,
    });

    await findBannedAccountKeywords(user);
    await reportLib.preemptiveModerationForUserProfileText(user);
    await checkForSpamHandles(user, user.firstName);
  }));

  router.put('/anonymousProfileNickname', findUser, asyncHandler(async (req, res, next) => {
    if (!req.body.anonymousProfileNickname || req.body.anonymousProfileNickname.trim() === '') {
      return next(invalidInputError('Nickname cannot be blank'));
    }
    if (req.body.anonymousProfileNickname.length > 50) {
      return next(invalidInputError('Character limit exceeded'));
    }
    const { user } = req;
    if (user && !user.versionAtLeast('1.13.74')) {
      return next(notFoundError());
    }

    if(await User.findOne({anonymousProfileNickname: req.body.anonymousProfileNickname}, 'anonymousProfileNickname -_id')){
      return next(invalidInputError('Nickname already taken'));
    }

    try{
      user.anonymousProfileNickname = req.body.anonymousProfileNickname;
      await user.save();
      res.json({
        anonymousProfileNickname: user.anonymousProfileNickname,
      });
    }catch (error){
      console.log(error)
      return next(badRequestError('failed to save nickname'));
    }

  }));

  router.put('/birthday', asyncHandler(async (req, res, next) => {
    const { user } = req;

    // user allowed to change birthday only once
    if (user.changedBirthday) {
      return next(forbiddenError());
    }

    // validate input
    const birthday = new Date(Date.UTC(
      req.body.year,
      req.body.month - 1, // Javascript months are 0 indexed
      req.body.day,
    ));
    if (isNaN(birthday)) {
      return next(invalidInputError());
    }
    const age = getAge(birthday, user.timezone)
    if (age > 150) {
      return next(invalidInputError('Invalid birthday'));
    }

    // if birthday was already set, mark it as changed
    if (user.birthday) {
      user.changedBirthday = true;
    }

    user.setBirthday(birthday, user.timezone);

    if (age < 18) {
      await reportLib.shadowBan(user, null, 'underage birthday', `Age: ${age}`);
      await reportLib.evaluateBanNotice(user);
    }

    await user.save();
    
    const response = {
      birthday: user.birthday,
    };

    if (user.shadowBanned && user.banNotice) {
      response.banNotice = userLib.formatBanNotice(user);
    }

    res.json(response);
  }));

  router.put('/gender', asyncHandler(async (req, res, next) => {
    const { user } = req;
    user.gender = req.body.gender;
    user.genderPreferenceHash = genderPreferenceLib.hashGenderPreference(user.gender, user.preferences.dating, user.preferences.friends);

    if (user.gender === 'female' && user.verification.method === 'pose' && user.metrics.lastSeenWeb) {
      await reportLib.createReport(
        user,
        null,
        ['Auto-report due to web pose verification'],
      );
    }
    // Shadowban non-binary new scammers
    if (moment().diff(user.createdAt, 'days') < 3 && user.metrics.lastSeenWeb && user.gender === 'non-binary' && user.verification?.method === 'pose' && !user.shadowBanned) {
      await reportLib.shadowBan(user, null, 'non-binary AI pose', null);
    }
    await user.save()
      .catch((err) => {
        if (err.name == 'ValidationError') return next(invalidInputError());
        return next(applicationError());
      });

    res.json({
      gender: user.gender,
    });
  }));

  router.put('/handle', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    if (!req.body.handle) {
      user.handle = undefined;
      await user.save();
      res.json({});
    } else {
      user.handle = req.body.handle;
      try {
        await user.save();
      } catch (err) {
        console.log(err);
        if (err.code == 11000) { // duplicate key error
          return next(conflictError(req.__('The handle you have chosen is already taken. Please select a different handle.')));
        }
        if (err.errors && err.errors.handle) {
          return next(invalidInputError(req.__('Handles are limited to 30 characters and must contain only letters, numbers, periods, and underscores.')));
        }
        return next(applicationError());
      }
      res.json({});
    }
  }));

  router.put('/searchable', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    user.searchable = req.body.searchable;
    await user.save()
    res.json({});
  }));

  router.put('/hidden', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    if (user._id == chatLib.BOO_SUPPORT_ID) {
      return res.json({});
    }

    user.hidden = req.body.hidden;
    user.calculateViewableInDailyProfiles();
    await user.save();
    res.json({});
  }));

  router.put('/location', asyncHandler(async (req, res, next) => {
    const { user } = req;

    if (user.disableLocationUpdates) {
      console.log(`Skipping location update for user ${user._id}`);
      return res.json({
        ...locationLib.getMyLocationFields(user),
      });
    }

    let { longitude } = req.body;
    let { latitude } = req.body;
    if (isNaN(longitude) || isNaN(latitude)) {
      return next(invalidInputError());
    }

    longitude = Number(longitude);
    latitude = Number(latitude);

    if (longitude < -180 || longitude > 180) {
      return next(invalidInputError());
    }
    if (latitude < -90 || latitude > 90) {
      return next(invalidInputError());
    }

    const location = {
      type: 'Point',
      coordinates: [
        longitude,
        latitude,
      ],
    };

    const firstLocation = user.location === undefined;

    const address = locationLib.getAddress(location, req.user.timezone);

    /*
    if (user.security.locationHistory.length > 0 && user.actualLocation) {
      const start = {
        latitude: user.actualLocation.coordinates[1],
        longitude: user.actualLocation.coordinates[0],
      };
      const end = {
        latitude,
        longitude,
      };
      const distanceKm = haversine(start, end);
      const priorTime = user.security.locationHistory[0].date.getTime();
      const durationHr = (Date.now() - priorTime) / (3600 * 1000);
      const speedKmph = distanceKm / durationHr;
      if (distanceKm > 100 && speedKmph > 1000) {
        await reportLib.createReport(
          user,
          null,
          ['Auto-report: location changed too quickly'],
          `${user.actualCity}, ${user.actualState}, ${user.actualCountry} -> ${address.city}, ${address.state}, ${address.country}`,
          `Distance: ${distanceKm.toFixed(2)} km, duration: ${durationHr.toFixed(2)} hours, speed: ${speedKmph.toFixed(2)} km/h`,
        );
      }
    }
    */

    /*
    // check if user is from a banned location
    const bannedLocations = [
      // latitude, longitude, label
      [14.538650512695312, 121.00178085627114, 'SA Rivendell'],
      [14.543657256200772, 120.98876069649104, 'SA Rivendell'],
    ];
    for (const location of bannedLocations) {
      const start = {
        latitude: location[0],
        longitude: location[1],
      };
      const end = {
        latitude,
        longitude,
      };
      const distanceKm = haversine(start, end);
      if (distanceKm < 1) {
        await reportLib.autoShadowBan(user, 'banned location', location[2]);
      }
    }

    if (address.city == 'Wan Chai') {
      await reportLib.autoShadowBan(user, 'banned location', 'Wan Chai');
    }
    if (address.state == 'Guangdong') {
      await reportLib.autoShadowBan(user, 'banned location', 'Guangdong');
    }
    */

    user.security.locationHistory.unshift({
      date: Date.now(),
      location,
      countryCode: address.countryCode,
      country: address.country,
      state: address.state,
      city: address.city,
    });
    if (user.security.locationHistory.length > 10) {
      user.security.locationHistory.pop();
    }

    if (user.actualCountryCode != address.countryCode) {
      user.security.internationalLocationHistory.unshift({
        date: Date.now(),
        location,
        countryCode: address.countryCode,
        country: address.country,
        state: address.state,
        city: address.city,
      });
      if (user.security.internationalLocationHistory.length > 10) {
        user.security.internationalLocationHistory.pop();
      }
    }

    if (user.metro === undefined || user.city != address.city) {
      user.metro = metrosLib.getMetro(location);
    }

    if (user.locationOverride.city) {
      // disable location override if user's location changes too much
      let nearbyCities = locationLib.getNearbyCities(location);
      let found = nearbyCities.find(x => x.city == user.locationOverride.city && x.state == user.locationOverride.state && x.countryCode == user.locationOverride.countryCode);
      if (!found) {
        user.originalLocation = undefined;
        user.locationOverride = undefined;
      }
    }

    user.actualLocation = location;
    user.actualCountryCode = address.countryCode;
    user.actualCountry = address.country;
    user.actualState = address.state;
    user.actualCity = address.city;
    if (!user.teleportLocation && !user.locationOverride.city) {
      user.setLocation(location, address.countryCode);
      user.country = address.country;
      user.state = address.state;
      user.city = address.city;
    }

    if (!user.config.show_sort_nearby_option || !user.config.show_distance_filter) {
      const userCount5Miles = await locationStatLib.getUserCount5Miles(user);
      user.numUsersWithinCalculatedAt = Date.now();
      user.numUsersWithin5Miles = userCount5Miles;
      if (userCount5Miles > constants.getLocalDensityThreshold()) {
        user.config.show_sort_nearby_option = true;
        user.config.show_distance_filter = true;
      }
    }

    if (!user.numUsersWithinCalculatedAt) {
      user.numUsersWithinCalculatedAt = Date.now();
      user.numUsersWithin5Miles = await locationStatLib.getUserCount5Miles(user);
    }

    user.calculateViewableInDailyProfiles();
    await user.save();

    await socketLib.onLocationUpdated(user);

    await checkTimezoneCountry(user);

    /*
    if (user.country == 'Australia' && user.state == 'Victoria' && user.prompts.length == 0) {
      await reportLib.createReport(
        user,
        null,
        ['Auto-report due to Victoria, Australia + no prompts'],
        'Auto-report due to Victoria, Australia + no prompts',
      );
    }
    */

    if (user.localStats.numLocalUsers === undefined) {
      // run in background
      User.computeLocalStats(user._id);
    }

    if (user.localStats.numLocalUsersTargetGender === undefined) {
      await profilesLib.countNumLocalUsersTargetGender(user);
    }

    res.json({
      ...locationLib.getMyLocationFields(user),
    });

    // Update user's location in embedding collection
    await userLib.updateLocationInEmbeddings(user);
  }));

  router.put('/hideCity', findUser, asyncHandler(async (req, res, next) => {
    const { hideCity } = req.body;
    if (hideCity != true && hideCity != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.hideCity = hideCity;
    await user.save();

    return res.json({
      ...locationLib.getMyLocationFields(user),
    });
  }));

  router.put('/hideReadReceipts', findUser, asyncHandler(async (req, res, next) => {
    const { hideReadReceipts } = req.body;
    if (hideReadReceipts != true && hideReadReceipts != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.hideReadReceipts = hideReadReceipts;
    await user.save();

    return res.json({});
  }));

  router.put('/hideQuestions', findUser, asyncHandler(async (req, res, next) => {
    const hide = req.body.hideQuestions;
    if (hide != true && hide != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.hideQuestions = hide;
    await user.save();

    return res.json({});
  }));

  router.put('/hideComments', findUser, asyncHandler(async (req, res, next) => {
    const hide = req.body.hideComments;
    if (hide != true && hide != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.hideComments = hide;
    await user.save();

    return res.json({});
  }));

  router.put('/hideHoroscope', asyncHandler(async (req, res, next) => {
    const hide = req.body.hideHoroscope;
    if (hide != true && hide != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.hideHoroscope = hide;
    await user.save();

    return res.json({});
  }));

  router.put('/hideLocation', asyncHandler(async (req, res, next) => {
    const { user } = req;

    const hide = req.body.hideLocation;
    if (hide != true && hide != false) {
      return next(invalidInputError());
    }

    if (hide && !premiumLib.isPremium(user)) {
      return next(forbiddenError());
    }

    user.hideLocation = hide;
    user.calculateViewableInDailyProfiles();
    await user.save();

    return res.json({});
  }));

  router.put('/hideProfileViews', asyncHandler(async (req, res, next) => {
    const { user } = req;

    const hide = req.body.hideProfileViews;
    if (hide != true && hide != false) {
      return next(invalidInputError());
    }

    if (hide && !premiumLib.isPremiumV1OrGodMode(user)) {
      return next(forbiddenError());
    }

    user.hideProfileViews = hide;
    await user.save();

    return res.json({});
  }));

  router.put('/showMyInfinityStatus', asyncHandler(async (req, res, next) => {
    const { user } = req;

    const show = req.body.showMyInfinityStatus;
    if (show != true && show != false) {
      return next(invalidInputError());
    }

    if (show && !premiumLib.isPremium(user)) {
      return next(forbiddenError());
    }

    user.showMyInfinityStatus = show;
    await user.save();

    return res.json({});
  }));

  router.put('/optOutOfAdTargeting', findUser, asyncHandler(async (req, res, next) => {
    req.user.optOutOfAdTargeting = req.body.optOutOfAdTargeting;
    if (req.user.optOutOfAdTargeting) {
      req.user.advertisingId = undefined;
    }
    await req.user.save();

    return res.json({});
  }));

  router.put('/autoplay', asyncHandler(async (req, res, next) => {
    req.user.autoplay = req.body.autoplay;
    await req.user.save();

    return res.json({});
  }));

  router.put('/approveAllFollowers', findUser, asyncHandler(async (req, res, next) => {
    const hide = req.body.approveAllFollowers;
    if (hide != true && hide != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.approveAllFollowers = hide;
    await user.save();

    return res.json({});
  }));

  router.put('/autoFollowLikes', asyncHandler(async (req, res, next) => {
    const bool = req.body.autoFollowLikes;
    if (bool != true && bool != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.autoFollowLikes = bool;
    await user.save();

    return res.json({});
  }));

  router.put('/autoFollowMatches', asyncHandler(async (req, res, next) => {
    const bool = req.body.autoFollowMatches;
    if (bool != true && bool != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.autoFollowMatches = bool;
    await user.save();

    return res.json({});
  }));

  router.put('/darkMode', asyncHandler(async (req, res, next) => {
    const input = req.body.darkMode;
    if (input != true && input != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.darkMode = input;
    await user.save();

    return res.json({});
  }));

  router.put('/useMetricSystem', asyncHandler(async (req, res, next) => {
    const input = req.body.useMetricSystem;
    if (input != true && input != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.useMetricSystem = input;
    await user.save();

    return res.json({});
  }));

  router.put('/vibrationsDisabled', asyncHandler(async (req, res, next) => {
    const input = req.body.vibrationsDisabled;
    if (input != true && input != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.vibrationsDisabled = input;
    await user.save();

    return res.json({});
  }));

  router.put('/dataSaver', asyncHandler(async (req, res, next) => {
    const input = req.body.dataSaver;
    if (input != true && input != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.dataSaver = input;
    await user.save();

    return res.json({});
  }));

  router.put('/dataSaving', asyncHandler(async (req, res, next) => {
    const { user } = req;
    user.dataSaving = req.body.dataSaving;
    await user.save();

    return res.json({});
  }));

  router.put('/hideMyFollowerCount', asyncHandler(async (req, res, next) => {
    const input = req.body.hideMyFollowerCount;
    if (input != true && input != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.hideMyFollowerCount = input;
    await user.save();

    return res.json({});
  }));

  router.put('/hideMyAwards', asyncHandler(async (req, res, next) => {
    const { hideMyAwards } = req.body;
    if (typeof hideMyAwards !== 'boolean') {
      return next(invalidInputError());
    }

    const { user } = req;
    user.hideMyAwards = hideMyAwards;
    await user.save();

    return res.json({});
  }));

  router.put('/hideMyKarma', asyncHandler(async (req, res, next) => {
    const { hideMyKarma } = req.body;
    if (typeof hideMyKarma !== 'boolean') {
      return next(invalidInputError());
    }

    const { user } = req;
    user.hideMyKarma = hideMyKarma;
    await user.save();

    return res.json({});
  }));

  router.put('/hideMyAge', asyncHandler(async (req, res, next) => {
    const { hideMyAge } = req.body;
    const { user } = req;
    if (typeof hideMyAge !== 'boolean') {
      return next(invalidInputError());
    }
    if (!premiumLib.isPremium(user)) {
      return next(forbiddenError());
    }
    user.hideMyAge = hideMyAge;
    await user.save();

    return res.json({});
  }));

  router.put('/changeHomeScreenToSocial', asyncHandler(async (req, res, next) => {
    const input = req.body.changeHomeScreenToSocial;
    if (input != true && input != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.changeHomeScreenToSocial = input;
    await user.save();

    return res.json({});
  }));

  router.put('/messagesTheme', asyncHandler(async (req, res, next) => {
    const input = req.body.messagesTheme;
    if (!Number.isInteger(input)) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.messagesTheme = input;
    await user.save();

    return res.json({});
  }));

  router.put('/instantMatch', findUser, asyncHandler(async (req, res, next) => {
    const { instantMatch } = req.body;
    if (instantMatch != true && instantMatch != false) {
      return next(invalidInputError());
    }

    const { user } = req;
    user.instantMatchEnabled = instantMatch;
    await user.save();

    return res.json({});
  }));

  router.put('/languages', asyncHandler(async (req, res, next) => {
    const { languages } = req.body;

    if (!Array.isArray(languages)
      || languages.length == 0
      || languages.length > languageCodes.length
      || hasDuplicates(languages)
      || languages.some((x) => !languageCodes.includes(x))
    ) {
      return next(invalidInputError());
    }

    req.user.languages = req.body.languages;
    await req.user.save();

    return res.json({});
  }));

  router.put('/ethnicities', asyncHandler(async (req, res, next) => {
    req.user.ethnicities = removeDuplicates(req.body.ethnicities.map(renameEthnicity));
    if (req.user.ethnicities.length === 0) {
      req.user.ethnicities = undefined;
    }
    await req.user.save();

    return res.json({});
  }));

  router.put('/spotify', asyncHandler(async (req, res, next) => {
    const arr = req.body.artists;

    if (arr) {
      if (!Array.isArray(arr)
        || arr.length > 10
        || arr.some((x) => typeof x.name !== 'string' || x.name.length == 0
            || typeof x.picture !== 'string' || x.picture.length == 0
            || typeof x.link !== 'string' || x.link.length == 0)
      ) {
        return next(invalidInputError());
      }
    }

    req.user.spotify = req.body;
    await req.user.save();

    return res.json({});
  }));

  router.put('/customPersonalityCompatibility', asyncHandler(async (req, res, next) => {

    req.user.customPersonalityCompatibility = req.body.customPersonalityCompatibility;
    await req.user.save();

    return res.json({});
  }));

  router.put('/notificationSettings', findUser, asyncHandler(async (req, res, next) => {
    // update new categories when old categories updated, old categories only accesible by old version user
    if (req.body.pushNotificationSettings.commentLikes !== undefined){
      req.body.pushNotificationSettings.commentLikesMatches = req.body.pushNotificationSettings.commentLikes
      req.body.pushNotificationSettings.commentLikesOtherSouls = req.body.pushNotificationSettings.commentLikes
    }

    if (req.body.pushNotificationSettings.commentReplies !== undefined){
      req.body.pushNotificationSettings.commentRepliesMatches = req.body.pushNotificationSettings.commentReplies
      req.body.pushNotificationSettings.commentRepliesOtherSouls = req.body.pushNotificationSettings.commentReplies
    }

    if(req.body.pushNotificationSettings.friendPosts !== undefined && req.body.pushNotificationSettings.friendStories === undefined){
      req.body.pushNotificationSettings.friendStories = req.body.pushNotificationSettings.friendPosts
    }

    if(req.body.pushNotificationSettings.dailyPush !== undefined){
      req.body.pushNotificationSettings.dailyFacts = req.body.pushNotificationSettings.dailyPush
      req.body.pushNotificationSettings.questionOfTheDay = req.body.pushNotificationSettings.dailyPush
      req.body.pushNotificationSettings.newSoulsNearby = req.body.pushNotificationSettings.dailyPush
    }

    req.user.pushNotificationSettings = req.body.pushNotificationSettings;
    await req.user.save()
      .catch((err) => {
        if (err.name == 'ValidationError') return next(invalidInputError());
        return next(applicationError());
      });

    return res.json({});
  }));

  router.put('/wingmanSettings', asyncHandler(async (req, res, next) => {
    req.user.wingmanSettings = req.body.wingmanSettings;
    await req.user.save()
      .catch((err) => {
        if (err.name == 'ValidationError') return next(invalidInputError());
        return next(applicationError());
      });

    return res.json({});
  }));

  router.put('/description', findUser, asyncHandler(async (req, res, next) => {
    if (req.body.description == undefined) {
      return next(badRequestError());
    }
    if (req.body.description.length > 10000) {
      return next(invalidInputError('Character limit exceeded'));
    }
    const { user } = req;
    user.originalFields.description = req.body.description;
    user.description = removeBannedKeywords(req.body.description);
    user.description = removeHiddenProfileText(user, user.description);

    const savedUser = await user.save();
    await User.updateSearchFields(req.user._id);
    await updateUserScore(user, { description: 1 });
    const coinReward = await coinsLib.onBiographyUpdated(savedUser);
    socketLib.sendCoinRewards(req.uid, coinReward);

    if (!process.env.TESTING) {
      res.json({
        description: savedUser.description,
        coinReward: coinReward || undefined,
      });
    }

    await findBannedAccountKeywords(user);
    await reportLib.preemptiveModerationForUserProfileText(user);

    keyword = autoBanUnderage.find((x) => new RegExp(`\\b${x}\\b`).test(req.body.description.toLowerCase()));
    if (keyword) {
      await reportLib.createReport(
        user,
        null,
        ['Auto-report: underage keywords detected in bio'],
        keyword,
      );
    }

    await checkForSpamHandles(user, user.description);

    if (process.env.TESTING) {
      res.json({
        description: savedUser.description,
        coinReward: coinReward || undefined,
      });
    }
  }));

  router.post('/audioDescription', s3.uploadDescriptionAudio.single('audio'), asyncHandler(async (req, res, next) => {
    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError());
    }
    console.log(req.file);

    const { user } = req;
    if (user.audioDescription) {
      console.log('Replacing audio: ', user.audioDescription);
      await s3.deletePicture(user.audioDescription);
      user.audioDescriptionTranscription = undefined;
    }
    user.audioDescription = req.file.key;
    const transcription = await transcribeAudio(req.file.key);
    if (transcription) {
      user.audioDescriptionTranscription = transcription;
    }
    if (req.body.waveform && typeof req.body.waveform === 'string') {
      const waveform = JSON.parse(req.body.waveform);
      user.audioDescriptionWaveform = waveform;
      user.audioDescriptionDuration = req.body.duration;
    }
    await user.save();
    await grantAudioDescriptionReward(user);

    return res.json({
      audioDescription: user.audioDescription,
      audioDescriptionWaveform: user.audioDescriptionWaveform,
      audioDescriptionDuration: user.audioDescriptionDuration,
    });
  }));

  router.delete('/audioDescription', asyncHandler(async (req, res, next) => {
    const { user } = req;
    if (!user.audioDescription) {
      return next(notFoundError());
    }

    await s3.deletePicture(user.audioDescription);

    user.audioDescription = undefined;
    user.audioDescriptionWaveform = undefined;
    user.audioDescriptionDuration = undefined;
    user.audioDescriptionTranscription = undefined;
    await user.save();

    return res.json({});
  }));

  router.put('/education', findUser, asyncHandler(async (req, res, next) => {
    if (req.body.education == undefined) {
      return next(badRequestError());
    }
    if (req.body.education.length > 50) {
      return next(invalidInputError('Character limit exceeded'));
    }
    const { user } = req;
    user.originalFields.education = req.body.education;
    user.education = removeBannedKeywords(req.body.education);
    user.education = removeHiddenProfileText(user, user.education);

    const savedUser = await user.save();
    await User.updateSearchFields(req.user._id);

    if (!process.env.TESTING) {
      res.json({});
    }

    await findBannedAccountKeywords(user);
    await reportLib.preemptiveModerationForUserProfileText(user);
    await checkForSpamHandles(user, user.education);

    if (process.env.TESTING) {
      res.json({});
    }
  }));

  router.put('/work', findUser, asyncHandler(async (req, res, next) => {
    if (req.body.work == undefined) {
      return next(badRequestError());
    }
    if (req.body.work.length > 50) {
      return next(invalidInputError('Character limit exceeded'));
    }

    const { user } = req;
    user.originalFields.work = req.body.work;
    user.work = removeBannedKeywords(req.body.work);
    user.work = removeHiddenProfileText(user, user.work);

    const savedUser = await user.save();
    await User.updateSearchFields(req.user._id);

    if (!process.env.TESTING) {
      res.json({});
    }

    await findBannedAccountKeywords(user);
    await reportLib.preemptiveModerationForUserProfileText(user);
    await checkForSpamHandles(user, user.work);

    if (process.env.TESTING) {
      res.json({});
    }
  }));

  router.put('/enneagram', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { enneagram } = req.body;
    let grantCoin = false;
    if (enneagram === undefined || enneagram === null) {
      user.enneagram = undefined;
    } else if (enneagrams.indexOf(enneagram) == -1) {
      return next(invalidInputError());
    } else {
      user.enneagram = enneagram;
      grantCoin = true;
    }

    await user.save();
    if (grantCoin) {
      await grantEnneagramReward(user);
    }

    if (req.body.answers) {
      const timezone = user.timezone;
      let country;
      if (timezone && momentTz.tz.zone(timezone) != null) {
        country = locationLib.getCountryNameFromTimezone(timezone);
      }

      await EnneagramQuizResult.create({
        user: req.uid,
        quiz: req.body.answers,
        enneagram,
        timezone,
        country,
        deviceId: user.deviceId,
      });
    }

    res.json({});
  }));

  router.put('/relationshipStatus', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { relationshipStatus } = req.body;
    if (relationshipStatus === undefined || relationshipStatus === null) {
      user.relationshipStatus = undefined;
    } else if (relationshipStatusChoices.indexOf(relationshipStatus) == -1) {
      return next(invalidInputError());
    }  else {
      user.relationshipStatus = relationshipStatus;
    }
    await user.save();
    return res.json({});
  }));

  router.put('/datingSubPreferences', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { datingSubPreferences } = req.body;
    if (datingSubPreferences === undefined || datingSubPreferences === null) {
      user.datingSubPreferences = undefined;
      user.datingPreferencesPopupClosed = true
    } else if (datingSubPreferencesChoices.indexOf(datingSubPreferences) == -1) {
      return next(invalidInputError());
    }  else {
      user.datingSubPreferences = datingSubPreferences;
      user.datingPreferencesPopupClosed = true
    }
    await user.save();
    return res.json({});
  }));

  router.put('/relationshipType', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { relationshipType } = req.body;
    if (relationshipType === undefined || relationshipType === null) {
      user.relationshipType = undefined;
      user.relationshipTypePopupClosed = true
    } else if (relationshipTypeChoices.indexOf(relationshipType) == -1) {
      return next(invalidInputError());
    }  else {
      user.relationshipType = relationshipType;
      user.relationshipTypePopupClosed = true
    }
    await user.save();
    return res.json({});
  }));

  router.put('/height', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { height } = req.body;

    user.height = height;
    await user.save();
    res.json({});
  }));

  router.put('/fcmToken', asyncHandler(async (req, res, next) => {
    const { fcmToken } = req.body;
    if (!fcmToken || typeof fcmToken !== 'string' || fcmToken.length > 4096) {
      return next(invalidInputError());
    }

    req.user.fcmToken = fcmToken;
    req.user.fcmTokenUpdatedAt = Date.now();
    if (req.user.fcmTokenFirstUpdatedOnDay == undefined && req.user.createdAt >= new Date('2024-07-20')) {
      req.user.fcmTokenFirstUpdatedOnDay = moment().diff(req.user.createdAt, 'days');
    }
    await req.user.save();

    await socketLib.onFcmTokenUpdated(req.user);

    await notificationLib.resetNotificationBadgeCount(req.uid);

    res.json({});
  }));

  router.put('/email', updateSignInInfo, asyncHandler(async (req, res, next) => {
    const coinReward = await coinsLib.onEmailUpdated(req.user);
    socketLib.sendCoinRewards(req.uid, coinReward);
    res.json({
      coinReward: coinReward || undefined,
    });
  }));

  router.get('/preferences', findUser, (req, res, next) => {
    const { user } = req;
    res.json(getPreferences(user));
  });

  router.get('/karma', (req, res, next) => {
    const { user } = req;
    res.json({
      karma: user.karma
    });
  });

  router.get('/verificationStatus', (req, res, next) => {
    const { user } = req;
    res.json({
      verificationStatus: user.verification.status,
      rejectionReason: user.verification.rejectionReason,
    });
  });

  router.patch('/utm', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    userLib.processUtm(req, user);
    await user.save();
    res.json({});
  }));

  router.patch('/preferences', asyncHandler(async (req, res, next) => {
    const { user } = req;

    const newUser = user.preferences?.dating?.length == 0 && user.preferences?.friends?.length == 0;

    let prefGlobal = user.preferences.global
    let prefDistance = user.preferences.distance
    let prefDating = user.preferences.dating
    let prefFriends = user.preferences.friends
    let prefGender = user.preferences.gender
    let prefPurpose = user.preferences.purpose

    if (req.body.hasOwnProperty('distance')) {
      const { distance } = req.body;
      if (isNaN(distance)
          || distance < 0
          || distance > 12500) {
        return next(invalidInputError());
      }
      if (distance > constants.maxDistanceFilter && !(isPremium(user) || isInterplanetary(user))) {
        return next(forbiddenError('Upgrade to premium to access Infinity Distance'));
      }
      prefDistance = distance;
      prefGlobal = distance >= constants.maxDistanceFilter;
    }
    if (req.body.hasOwnProperty('distance2')) {
      const { distance2 } = req.body;
      if (isNaN(distance2)
          || distance2 < 0
          || distance2 > 100) {
        return next(invalidInputError());
      }
      user.preferences.distance2 = distance2;
    }
    if (req.body.hasOwnProperty('minAge')) {
      const { minAge } = req.body;
      if (isNaN(minAge)
          || minAge < 18
          || minAge > 200) {
        return next(invalidInputError());
      }
      user.preferences.minAge = minAge;
    }
    if (req.body.hasOwnProperty('maxAge')) {
      const { maxAge } = req.body;
      if (isNaN(maxAge)
          || maxAge < 18
          || maxAge > 200) {
        return next(invalidInputError());
      }
      user.preferences.maxAge = maxAge;
    }
    if (req.body.hasOwnProperty('minHeight')) {
      const { minHeight } = req.body;
      user.preferences.minHeight = minHeight;
    }
    if (req.body.hasOwnProperty('maxHeight')) {
      const { maxHeight } = req.body;
      user.preferences.maxHeight = maxHeight;
    }
    if (req.body.hasOwnProperty('bioLength')) {
      const { bioLength } = req.body;
      if (isNaN(bioLength)) {
        return next(invalidInputError());
      }
      user.preferences.bioLength = bioLength;
    }
    if (req.body.hasOwnProperty('gender')
        && !req.body.hasOwnProperty('dating')
        && !req.body.hasOwnProperty('friends')) {
      const { gender } = req.body;
      if (!Array.isArray(gender)
          || !gender.every((entry) => ['male', 'female', 'non-binary'].includes(entry))
          || hasDuplicates(gender)) {
        return next(invalidInputError());
      }
      prefGender = gender;
    }
    if (req.body.hasOwnProperty('personality')) {
      const { personality } = req.body;
      if (!Array.isArray(personality)
          || !personality.every((entry) => personalityLib.isValidMbti(entry))
          || hasDuplicates(personality)) {
        return next(invalidInputError());
      }
      user.preferences.personality = personality;
    }
    if (req.body.hasOwnProperty('purpose')
        && !req.body.hasOwnProperty('dating')
        && !req.body.hasOwnProperty('friends')) {
      const { purpose } = req.body;
      if (!Array.isArray(purpose)
          || !purpose.every((entry) => ['dating', 'friends'].includes(entry))
          || hasDuplicates(purpose)) {
        return next(invalidInputError());
      }
      prefPurpose = purpose;
    }
    if (req.body.hasOwnProperty('dating')) {
      const gender = req.body.dating;
      if (!Array.isArray(gender)
          || !gender.every((entry) => ['male', 'female', 'non-binary'].includes(entry))
          || hasDuplicates(gender)) {
        return next(invalidInputError());
      }
      prefDating = gender;
      prefPurpose = undefined;
      prefGender = undefined;
    }
    if (req.body.hasOwnProperty('friends')) {
      const gender = req.body.friends;
      if (!Array.isArray(gender)
          || !gender.every((entry) => ['male', 'female', 'non-binary'].includes(entry))
          || hasDuplicates(gender)) {
        return next(invalidInputError());
      }
      prefFriends = gender;
      prefPurpose = undefined;
      prefGender = undefined;
    }
    if (req.body.hasOwnProperty('local')) {
      const x = req.body.local;
      if (x !== true && x !== false) {
        return next(invalidInputError());
      }
      user.preferences.local = x;
      prefDistance = null;
    }
    if (req.body.hasOwnProperty('global')) {
      const x = req.body.global;
      if (x !== true && x !== false) {
        return next(invalidInputError());
      }
      prefGlobal = x;
      prefDistance = null;
    }
    if (req.body.hasOwnProperty('showVerifiedOnly')) {
      const x = req.body.showVerifiedOnly;
      if (x !== true && x !== false) {
        return next(invalidInputError());
      }
      if (x == true && !user.isVerified()) {
        return next(forbiddenError());
      }
      user.preferences.showVerifiedOnly = x;
    }
    if (req.body.hasOwnProperty('showToVerifiedOnly')) {
      const x = req.body.showToVerifiedOnly;
      if (x == true && !user.isVerified()) {
        return next(forbiddenError());
      }
      user.preferences.showToVerifiedOnly = x;
    }
    if (req.body.hasOwnProperty('sameCountryOnly')) {
      const x = req.body.sameCountryOnly;
      user.preferences.sameCountryOnly = x;
    }
    if (req.body.hasOwnProperty('showUsersOutsideMyRange')) {
      const x = req.body.showUsersOutsideMyRange
      if (x !== true && x !== false) {
        return next(invalidInputError());
      }
      user.preferences.showUsersOutsideMyRange = x;
    }
    if (req.body.hasOwnProperty('countries') && isPremium(user)) {
      const { countries } = req.body;
      if (!countries) {
        user.preferences.countries = undefined;
      } else {
        if (!Array.isArray(countries)
            || !countries.every((c) => constants.countryCodes.includes(c))
            || hasDuplicates(countries)) {
          return next(invalidInputError());
        }
        user.preferences.countries = countries;
        if (countries.length == 0) {
          user.preferences.countries = undefined;
        }
      }
    }
    if (req.body.hasOwnProperty('interestNames')) {
      if (!req.body.interestNames) {
        user.preferences.interestNames = undefined;
      } else {
        const interestNames = interestLib.cleanInterestNames(req.body.interestNames);
        user.preferences.interestNames = interestNames;
      }
    }
    if (req.body.hasOwnProperty('excludedInterestNames')) {
      if (!req.body.excludedInterestNames) {
        user.preferences.excludedInterestNames = undefined;
      } else {
        const excludedInterestNames = interestLib.cleanInterestNames(req.body.excludedInterestNames);
        user.preferences.excludedInterestNames = excludedInterestNames;
      }
    }

    if(basic.checkIfCommonElementsInArray(user.preferences.interestNames, user.preferences.excludedInterestNames)){
      return next(invalidInputError());
    }

    if (req.body.hasOwnProperty('enneagrams')) {
      const selected = req.body.enneagrams;
      if (!selected) {
        user.preferences.enneagrams = undefined;
      } else {
        if (!Array.isArray(selected)
            || !selected.every((x) => enneagrams.includes(x))
            || hasDuplicates(selected)
        ) {
          return next(invalidInputError());
        }
        user.preferences.enneagrams = selected;
      }
    }
    if (req.body.hasOwnProperty('datingSubPreferences') /*&& isPremium(user)*/) {
      const { datingSubPreferences } = req.body;
      if (!datingSubPreferences) {
        user.preferences.datingSubPreferences = undefined;
      } else {
        if (!Array.isArray(datingSubPreferences)
            || !datingSubPreferences.every((c) => datingSubPreferencesChoices.includes(c))
            || hasDuplicates(datingSubPreferences)) {
          return next(invalidInputError());
        }
        user.preferences.datingSubPreferences = datingSubPreferences;
        if (datingSubPreferences.length == 0) {
          user.preferences.datingSubPreferences = undefined;
        }
      }
    }
    if (req.body.hasOwnProperty('relationshipType')) {
      const { relationshipType } = req.body;
      if (!relationshipType) {
        user.preferences.relationshipType = undefined;
      } else {
        if (!Array.isArray(relationshipType)
            || !relationshipType.every((c) => relationshipTypeChoices.includes(c))
            || hasDuplicates(relationshipType)) {
          return next(invalidInputError());
        }
        user.preferences.relationshipType = relationshipType;
        if (relationshipType.length == 0) {
          user.preferences.relationshipType = undefined;
        }
      }
    }
    if (req.body.hasOwnProperty('relationshipStatus') /*&& isPremium(user)*/) {
      const { relationshipStatus } = req.body;
      if (!relationshipStatus) {
        user.preferences.relationshipStatus = undefined;
      } else {
        if (!Array.isArray(relationshipStatus)
            || !relationshipStatus.every((c) => relationshipStatusChoices.includes(c))
            || hasDuplicates(relationshipStatus)) {
          return next(invalidInputError());
        }
        user.preferences.relationshipStatus = relationshipStatus;
        if (relationshipStatus.length == 0) {
          user.preferences.relationshipStatus = undefined;
        }
      }
    }
    if (req.body.hasOwnProperty('languages')) {
      const { languages } = req.body;

      if (!languages) {
        user.preferences.languages = undefined;
      } else {
        if (!Array.isArray(languages)
          || languages.length > languageCodes.length
          || hasDuplicates(languages)
          || !languages.every((x) => languageCodes.includes(x))
        ) {
          return next(invalidInputError());
        }
        user.preferences.languages = languages;
      }
    }
    if (req.body.hasOwnProperty('ethnicities')) {
      const { ethnicities } = req.body;

      if (!ethnicities || ethnicities.length == 0) {
        user.preferences.ethnicities = undefined;
      } else {
        user.preferences.ethnicities = removeDuplicates(ethnicities.map(renameEthnicity));
      }
    }
    if (req.body.hasOwnProperty('horoscopes')) {
      const selected = req.body.horoscopes;
      if (!selected) {
        user.preferences.horoscopes = undefined;
      } else {
        if (!Array.isArray(selected)
            || !selected.every((x) => horoscopes.includes(x))
            || hasDuplicates(selected)
        ) {
          return next(invalidInputError());
        }
        user.preferences.horoscopes = selected;
      }
    }
    if (req.body.hasOwnProperty('keywords')) {
      let keywords = req.body.keywords;
      if (!keywords) {
        user.preferences.keywords = undefined;
      } else {
        if (!Array.isArray(keywords)
            || !keywords.every((word) => ((word.length > 0) && (word.split(' ').length === 1)))
            || hasDuplicates(keywords)
        ) {
          return next(invalidInputError('must be an array of words not containing spaces'));
        }
        keywords = keywords.map(betweenPunctuations).filter((word) => word.length > 0);
        user.preferences.keywords = keywords;
      }
    }
    for (const key in moreAboutUserChoices) {
      if (req.body.hasOwnProperty(key)) {
        const values = req.body[key];

        if (!values) {
          user.preferences[key] = undefined;
        } else {
          const choices = moreAboutUserChoices[key];
          if (
            !Array.isArray(values)
            || values.length > choices.length
            || hasDuplicates(values)
            || values.some((x) => !choices.includes(x))) {
            throw invalidInputError();
          }
          user.preferences[key] = values;
        }
      }
    }
    if (req.body.hasOwnProperty('sexuality')) {
      const { sexuality } = req.body;
      if (!sexuality) {
        user.preferences.sexuality = undefined;
      } else {
        if (!Array.isArray(sexuality)
            || !sexuality.every((c) => sexualityChoices.includes(c))
            || hasDuplicates(sexuality)) {
          return next(invalidInputError());
        }
        user.preferences.sexuality = sexuality;
        if (sexuality.length == 0) {
          user.preferences.sexuality = undefined;
        }
      }
    }

    if (req.body.showUnspecified === null) {
      user.preferences.showUnspecified = {}
    } else if (req.body.hasOwnProperty('showUnspecified') && typeof req.body.showUnspecified === 'object') {
      const validKeys = ['relationshipStatus', 'datingSubPreferences', 'relationshipType','sexuality', 'enneagrams', 'exercise', 'educationLevel', 'drinking', 'smoking', 'kids', 'religion', 'ethnicities'];
      if (!user.preferences.showUnspecified) {
        user.preferences.showUnspecified = {};
      }
      for (const key of validKeys) {
        if (req.body.showUnspecified.hasOwnProperty(key)) {
          const val = req.body.showUnspecified[key];
          if (val !== true && val !== false) {
            return next(invalidInputError());
          }
          user.preferences.showUnspecified[key] = val;
        }
      }
    }

    if (prefGender) {
      prefDating = [];
      prefFriends = [];
      if (!prefPurpose || prefPurpose.includes('dating')) {
        prefDating = prefGender;
      }
      if (!prefPurpose || prefPurpose.includes('friends')) {
        prefFriends = prefGender;
      }
    }

    user.genderPreferenceHash = genderPreferenceLib.hashGenderPreference(user.gender, prefDating, prefFriends);

    user.preferences.global = prefGlobal
    user.preferences.distance = prefDistance
    user.preferences.dating = prefDating
    user.preferences.friends = prefFriends
    user.preferences.gender = prefGender
    user.preferences.purpose = prefPurpose

    if (newUser && (user.preferences?.dating?.length > 0 || user.preferences?.friends?.length > 0)) {
      // user completed onboarding for first time - assign pricing experiment config
      pricingConfigLib.assignExperimentConfig(user);
    }

    await user.save();
    res.json(getPreferences(user));
  }));

  router.patch('/preferences/countries', asyncHandler(async (req, res, next) => {
    const { user } = req;
    if (!isPremium(user)) {
      return next(forbiddenError());
    }

    if (req.body.countries) {
      let { countries } = req.body;
      if (!Array.isArray(countries)
          || !countries.every((c) => constants.countryCodes.includes(c))
          || hasDuplicates(countries)) {
        return next(invalidInputError());
      }
      countries = countries.filter(x => x != 'IO');
      if (countries.length > 0) {
        user.preferences.countries = countries;
      } else {
        user.preferences.countries = undefined;
      }
    } else {
      user.preferences.countries = undefined;
    }
    await user.save();
    res.json({});
  }));

  router.patch('/preferences/datingSubPreferences', asyncHandler(async (req, res, next) => {
    const { user } = req;
    // if (!isPremium(user)) {
    //   return next(forbiddenError());
    // }
    if (req.body.datingSubPreferences) {
      let { datingSubPreferences } = req.body;
      if (!Array.isArray(datingSubPreferences)
          || !datingSubPreferences.every((c) => datingSubPreferencesChoices.includes(c))
          || hasDuplicates(datingSubPreferences)) {
        return next(invalidInputError());
      }
      if (datingSubPreferences.length > 0) {
        user.preferences.datingSubPreferences = datingSubPreferences;
      } else {
        user.preferences.datingSubPreferences = undefined;
      }
    } else {
      user.preferences.datingSubPreferences = undefined;
    }
    await user.save();
    res.json({});
  }));


  router.patch('/preferences/relationshipStatus', asyncHandler(async (req, res, next) => {
    const { user } = req;
    // if (!isPremium(user)) {
    //   return next(forbiddenError());
    // }
    if (req.body.relationshipStatus) {
      let { relationshipStatus } = req.body;
      if (!Array.isArray(relationshipStatus)
          || !relationshipStatus.every((c) => relationshipStatusChoices.includes(c))
          || hasDuplicates(relationshipStatus)) {
        return next(invalidInputError());
      }
      if (relationshipStatus.length > 0) {
        user.preferences.relationshipStatus = relationshipStatus;
      } else {
        user.preferences.relationshipStatus = undefined;
      }
    } else {
      user.preferences.relationshipStatus = undefined;
    }
    await user.save();
    res.json({});
  }));

  router.patch('/preferences/relationshipType', asyncHandler(async (req, res, next) => {
    const { user } = req;
    if (req.body.relationshipType) {
      let { relationshipType } = req.body;
      if (!Array.isArray(relationshipType)
          || !relationshipType.every((c) => relationshipTypeChoices.includes(c))
          || hasDuplicates(relationshipType)) {
        return next(invalidInputError());
      }
      if (relationshipType.length > 0) {
        user.preferences.relationshipType = relationshipType;
      } else {
        user.preferences.relationshipType = undefined;
      }
    } else {
      user.preferences.relationshipType = undefined;
    }
    await user.save();
    res.json({});
  }));

  router.patch('/preferences/interests', asyncHandler(async (req, res, next) => {
    const { user } = req;

    if (req.body.interestNames) {
      const interestNames = interestLib.cleanInterestNames(req.body.interestNames);
      const interests = await interestLib.validateInterestNames(interestNames);
      if (!interests) {
        return next(invalidInputError());
      }
      const interestIds = interests.map((x) => x._id);
      user.preferences.interests = interestIds;
      user.preferences.interestNames = interestNames;
    } else if (req.body.interests) {
      const interestIds = req.body.interests;
      const interestNames = interestLib.getInterestNamesFromIds(interestIds);
      if (!interestNames) {
        return next(invalidInputError());
      }
      user.preferences.interests = interestIds;
      user.preferences.interestNames = interestNames;
    } else {
      user.preferences.interests = undefined;
      user.preferences.interestNames = undefined;
    }
    if(basic.checkIfCommonElementsInArray(user.preferences.interestNames, user.preferences.excludedInterestNames)){
      return next(invalidInputError());
    }
    await user.save();
    res.json({});
  }));

  router.patch('/preferences/enneagrams', asyncHandler(async (req, res, next) => {
    const { user } = req;

    if (req.body.enneagrams) {
      const selected = req.body.enneagrams;
      if (!Array.isArray(selected)
          || !selected.every((x) => enneagrams.includes(x))
          || hasDuplicates(selected)
      ) {
        return next(invalidInputError());
      }
      if (selected.length > 0) {
        user.preferences.enneagrams = selected;
      } else {
        user.preferences.enneagrams = undefined;
      }
    } else {
      user.preferences.enneagrams = undefined;
    }
    await user.save();
    res.json({});
  }));

  router.patch('/preferences/languages', asyncHandler(async (req, res, next) => {
    const { user } = req;

    if (req.body.languages) {
      const { languages } = req.body;

      if (!Array.isArray(languages)
        || languages.length > languageCodes.length
        || hasDuplicates(languages)
        || !languages.every((x) => languageCodes.includes(x))
      ) {
        return next(invalidInputError());
      }
      if (languages.length > 0) {
        user.preferences.languages = languages;
      } else {
        user.preferences.languages = undefined;
      }
    } else {
      user.preferences.languages = undefined;
    }
    await user.save();
    res.json({});
  }));

  router.patch('/preferences/horoscopes', asyncHandler(async (req, res, next) => {
    const { user } = req;

    if (req.body.horoscopes) {
      const selected = req.body.horoscopes;
      if (!Array.isArray(selected)
          || !selected.every((x) => horoscopes.includes(x))
          || hasDuplicates(selected)
      ) {
        return next(invalidInputError());
      }
      if (selected.length > 0) {
        user.preferences.horoscopes = selected;
      } else {
        user.preferences.horoscopes = undefined;
      }
    } else {
      user.preferences.horoscopes = undefined;
    }
    await user.save();
    res.json({});
  }));

  router.patch('/preferences/keywords', asyncHandler(async (req, res, next) => {
    const { user } = req;
    let { keywords } = req.body;

    if (keywords) {
      if (!Array.isArray(keywords)
          || !keywords.every((word) => ((word.length > 0) && (word.split(' ').length === 1)))
          || hasDuplicates(keywords)
      ) {
        return next(invalidInputError('must be an array of words not containing spaces'));
      }
      if (keywords.length > 0) {
        keywords = keywords.map(betweenPunctuations).filter((word) => word.length > 0);
        user.preferences.keywords = keywords;
      } else {
        user.preferences.keywords = undefined;
      }
    } else {
      user.preferences.keywords = undefined;
    }
    await user.save();
    res.json({
      keywords: keywords || [],
    });
  }));

  router.put('/incomingRequestsPreferences', asyncHandler(async (req, res, next) => {
    const { user } = req;
    user.incomingRequestsPreferences = req.body.incomingRequestsPreferences;
    if (user.incomingRequestsPreferences.custom) {
      user.incomingRequestsPreferences.custom.genderPreferenceHash = genderPreferenceLib.hashGenderPreference(
        user.gender,
        user.incomingRequestsPreferences.custom.dating,
        user.incomingRequestsPreferences.custom.friends
      );
      if (user.incomingRequestsPreferences.custom.countries && user.incomingRequestsPreferences.custom.countries.length == 0) {
        user.incomingRequestsPreferences.custom.countries = undefined;
      }
    }
    await user.save();
    return res.json({});
  }));

  router.put('/socialPreferences', asyncHandler(async (req, res, next) => {
    const { user } = req;
    user.socialPreferences = req.body.socialPreferences;
    await user.save();
    return res.json({});
  }));

  router.put('/socialPreferencesActivated', asyncHandler(async (req, res, next) => {
    const { user } = req;
    user.socialPreferencesActivated = req.body.socialPreferencesActivated;
    await user.save();
    return res.json({});
  }));

  router.put('/customFeeds', asyncHandler(async (req, res, next) => {
    const { user } = req;
    user.customFeeds = req.body.customFeeds;
    await user.save();
    return res.json({});
  }));

  router.put('/customFeed', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const index = user.customFeeds.findIndex(feed => feed.feedName == req.body.customFeed.feedName);
    if (index > -1) {
      user.customFeeds[index] = req.body.customFeed;
    } else {
      user.customFeeds.push(req.body.customFeed);
    }
    await user.save();
    return res.json({});
  }));

  router.delete('/customFeed', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const index = user.customFeeds.findIndex(feed => feed.feedName == req.body.feedName);
    if (index > -1) {
      user.customFeeds.splice(index, 1);
      await user.save();
    }
    return res.json({});
  }));

  router.get('/profilePrompts', findUser, (req, res) => {
    const formatted = promptsLib.promptsArray.map((p) => ({
      id: p.id,
      prompt: p.prompt,
      exampleAnswer: p.exampleAnswer,
    }));
    res.json({
      prompts: formatted,
    });
  });

  router.put('/profilePromptAnswers', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    let { prompts } = req.body;
    if (!Array.isArray(prompts) || prompts.length > 3) {
      return next(invalidInputError());
    }
    const needsPromptValidation = prompts.some(p => p && typeof p.prompt === 'string' && !p.id);
    let validPromptStrings = new Set();
    if (needsPromptValidation) {
      const promptList = await UsersAiTailoredPrompts.distinct('outputPrompts.prompt', { userId: user._id });

      validPromptStrings = new Set(promptList);
    }

    const isInvalid = function (p) {
      if (!p) {
        return false;
      }
      if (typeof p.answer !== 'string' || p.answer.length > 10000) {
        return true;
      }
      if (!p.id && !p.prompt) {
        return true;
      }
      if(p.id && typeof p.id != 'string'){
        return true;
      }
      if(p.prompt && typeof p.prompt != 'string'){
        return true;
      }
      if (typeof p.id === 'string') {
        return p.id.length > 100;
      }
      if (typeof p.prompt === 'string' && !p.id) {
        return !validPromptStrings.has(p.prompt);
      }

      return false;
    };

    if (prompts.some(isInvalid) || hasDuplicates(prompts.map((p) => p.id || p.prompt))) {
      return next(invalidInputError());
    }

    prompts = prompts.filter((p) => p.answer.length > 0).map((p) => ({
      id: p.id || undefined,
      prompt: p.id ? undefined : p.prompt,
      answer: p.answer,
    }));
    user.originalFields.prompts = prompts;

    user.prompts = prompts.map((p) => ({
      id: p.id || undefined,
      prompt: p.prompt || undefined,
      answer: removeHiddenProfileText(user, removeBannedKeywords(p.answer)),
    }));;

    await user.save();
    await User.updateSearchFields(req.user._id);
    await updateUserScore(user, { description: 1 });

    const promptRewards = await onPromptsUpdated(user);
    socketLib.sendCoinRewards(user._id, promptRewards);

    if (!process.env.TESTING) {
      res.json({});
    }

    await findBannedAccountKeywords(user);
    await reportLib.preemptiveModerationForUserProfileText(user);
    await checkForSpamHandles(user, user.prompts.map(x => x.answer).join(' '));

    if (process.env.TESTING) {
      res.json({});
    }
  }));

  router.get('/quizQuestions', (req, res) => {
    res.json({
      questions: personalityLib.getQuizQuestions(req.user.locale),
    });
  });

  router.get('/shortQuizQuestions', (req, res) => {
    res.json({
      questions: personalityLib.getShortQuizQuestions(req.user.locale),
    });
  });

  router.put('/personality', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;

    user.personality = {
      mbti: req.body.mbti,
    };
    if (!user.telepathyPreviewMbti) {
      user.telepathyPreviewMbti = req.body.mbti;
    }
    try {
      await user.save();
    } catch (err) {
      console.log(err);
      if (err.name == 'ValidationError') {
        return next(invalidInputError('Invalid mbti'));
      }
      return next(applicationError());
    }
    res.json({});
  }));

  router.put('/quizAnswers', findUser, asyncHandler(async(req, res, next) => {
    const { user } = req;
    // Sanitize input
    for (const [key, value] of Object.entries(req.body.answers)) {
      if (isNaN(value)) {
        return next(invalidInputError());
      }
      req.body.answers[key] = Number(value);
    }

    // Process quiz answers
    const personality = personalityLib.processQuiz(req.body.answers);

    // Save the result in history
    const personalityQuizResult = new PersonalityQuizResult(personality);
    personalityQuizResult.user = req.uid;
    if (user.timezone) {
      personalityQuizResult.timezone = user.timezone;
      personalityQuizResult.country = locationLib.getCountryNameFromTimezone(user.timezone);
    }

    await personalityQuizResult.save();
    // Update the user model's copy of the result
    user.personality = personality;
    if (!user.telepathyPreviewMbti) {
      user.telepathyPreviewMbti = personality.mbti;
    }
    await user.save();
    await grantQuizAnswerReward(user);
    const avatar = personalityLib.getAvatar(user.personality.mbti, user.locale);
    if (!user.appVersion || cmp(user.appVersion, '1.10.17') < 0) {
      // backwards compatibility
      return res.json({
        mbti: user.personality.mbti,
        avatar: avatar.avatar,
        description: avatar.description,
      });
    }
    const rv = personalityLib.getPersonality(user, user.locale);
    rv.description = avatar.description;
    res.json(rv);
  }));

  router.get('/recommendedPersonalities', findUser, (req, res, next) => {
    res.json(personalityLib.getRecommendedPersonalities(req.user));
  });

  router.get('/threeTierPersonalityRecommendations', findUser, (req, res, next) => {
    const result = personalityLib.getThreeTierPersonalityRecommendations(req.user);
    if (!result) {
      return next(notFoundError('You have not taken the personality test yet'));
    }
    res.json(result);
  });

  router.post('/relationshipBackgroundPicture', deprecated, (req, res, next) => next(notFoundError()));

  router.delete('/relationshipBackgroundPicture', deprecated, (req, res, next) => next(notFoundError()));

  router.post('/picture/v2', verifyPictureLimit, s3.multerUpload().single('image'), asyncHandler(async (req, res, next) => {
    // todo: replace with update to avoid overwriting changes
    // todo: handle partial failures or make transaction
    const { user } = req;

    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError('Image was not provided'));
    }
    console.log(req.file);

    const moderationService = new ImageModerationService(['Hive']);
    const moderationResults = await moderationService.moderatePicture(req.file.key);
    if (moderationResults.hive_v2) {
      console.log(`Inappropriate picture detected from user ${req.uid}`);
      return next(invalidInputError(req.__('There’s an issue with this photo, please select another one.')));
    }

    await handleApprovedProfilePicture(req.user, req.file.key, res);

    /* Disabled banned face check for profile picture because it leads to mistaken bans
    if (req.user.pictures.length === 1) {
      await scammerImageDetection(req.user, constants.IMAGE_DOMAIN + req.file.key);

      const bannedFace = await findBannedFace(req.file.key);
      if (bannedFace) {
        await reportLib.banDueToBannedFaceFound(req.user, 'profile picture', req.file.key, bannedFace.Face.ExternalImageId);
      }

      await reportLib.screenForScammer(req.user);
    }
    */

    await banUserIfFileIsAlreadyBanned(req.user, req.file.key);
    // create embedding of the image and update merged embedding
    await userLib.handleEmbeddingUpdate(req.user, 'add', req.file.key);
  }));

  router.post('/picture', deprecated, (req, res, next) => next(notFoundError()));

  router.post('/editPicture', verifyPictureExists, s3.multerUpload().single('image'), asyncHandler(async (req, res, next) => {
    // todo: replace with update to avoid overwriting changes
    // todo: handle partial failures or make transaction

    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError('Image was not provided'));
    }
    console.log(req.file);

    const moderationService = new ImageModerationService(['Hive']);
    const moderationResults = await moderationService.moderatePicture(req.file.key);
    if (moderationResults.hive_v2) {
      console.log(`Inappropriate picture detected from user ${req.uid}`);
      return next(invalidInputError(req.__('There’s an issue with this photo, please select another one.')));
    }

    await s3.deletePicture(req.query.id);

    const { user } = req;

    user.initOriginalPictures();
    const priorFirstPicture = user.pictures[0];

    const index = user.originalPictures.findIndex((id) => id == req.query.id);
    if (index >= 0) {
      user.originalPictures.set(index, req.file.key);
    }
    await reportLib.computePictures(user);

    const firstPictureChanged = priorFirstPicture != user.pictures[0];

    await checkForUpdateToNewReverification(user);
    if (!userLib.useNewReverification(user) && user.verification.status == 'verified') {
      if (user.livenessVerification?.compareFacesSuccess || user.livenessVerification?.manuallyCheckedResult) {
        const addResult = await addProfilePicture(user, user.livenessVerification, req.file.key);
        const removeResult = await removeProfilePicture(user, user.livenessVerification, req.query.id);
        if (removeResult == 'matches' && user.livenessVerification.matchingPictures.length == 0) {
          await user.setVerificationStatus('reverifying');
        }
        if (addResult == 'does not match') {
          await user.setVerificationStatus('reverifying');
        }
      } else {
        await user.setVerificationStatus('reverifying');
      }
    }
    if (shouldNewReverify(user) && firstPictureChanged) {
      if (user.verification.status === 'verified') {
        user.verification.reVerification = true;
      }
      const result = await verifyProfilePicture(user);
      if (result == 'manual') {
        await user.setVerificationStatus('reverifying');
      } else if (result == 'reject') {
        await user.setVerificationStatus('rejected', 'rejected during reverification');
        user.verification.rejectionReason = 'Make sure your first profile picture is a picture of you, and only you.';
      } else if (result == 'verify' && user.verification.status == 'rejected' && user.verification.reVerification) {
        await user.setVerificationStatus('verified', 'verified during reverification');
        user.verification.reVerification = undefined;
      }
    }

    if(req.query.batchId){
      await updateBatchStatus(user._id, req.query.batchId, batchStatus.SELECTED)
      user.aiGenPictures.push(req.file.key)
      user.metrics.numAIGenImageUsed += 1
    }

    await user.save();

    console.log(`Replaced picture: ${req.query.id}`);
    res.json(user.originalPictures);

    await findBannedAccountKeywords(user);

    await banUserIfFileIsAlreadyBanned(req.user, req.file.key);
    // create embedding of the new image and update merged embedding
    await userLib.handleEmbeddingUpdate(req.user, 'edit', req.file.key, req.query.id);
  }));

  router.post('/profileVideo', userMiddleware.checkVerified, verifyPictureLimit, s3.uploadProfileVideo.single('video'), asyncHandler(async (req, res, next) => {
    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError('Video was not provided'));
    }
    console.log(req.file);

    const status = await convertHls(req.file.key);
    if (status == 'COMPLETE') {
      await User.findByIdAndUpdate(req.uid, { $push: { convertedVideos: req.file.key } });
      const { hive_v2 } = await isVideoInappropriate(req.file.key, req.user);
      if (hive_v2) {
        return next(invalidInputError(req.__('There’s an issue with this video, please select another one.')));
      }
    }

    await handleApprovedProfilePicture(req.user, req.file.key, res);
  }));

  router.delete('/picture', asyncHandler(async (req, res, next) => {
    // todo: if picture is in user but not in s3, delete from user
    // todo: if picture is in s3 but not in user, delete from s3
    let { user } = req;
    user.initOriginalPictures();
    await user.save();
    const index = user.pictures.findIndex((id) => id == req.query.id);
    const originalPicturesIndex = user.originalPictures ? user.originalPictures.findIndex((id) => id == req.query.id) : -1;
    if (index < 0 && originalPicturesIndex < 0) {
      return next(notFoundError('The picture you are trying to delete could not be found'));
    }

    await s3.deletePicture(req.query.id);

    user = await User.findByIdAndUpdate(req.uid, { $pull: { pictures: req.query.id, originalPictures: req.query.id, convertedVideos: req.query.id } }, { new: true });
    await reportLib.computePictures(user);
    updateUserScore(user, { pictures: 1 });
    await checkForUpdateToNewReverification(user);
    if (!userLib.useNewReverification(user) && user.verification.status == 'verified') {
      if (user.livenessVerification?.compareFacesSuccess || user.livenessVerification?.manuallyCheckedResult) {
        const removeResult = await removeProfilePicture(user, user.livenessVerification, req.query.id);
        if (removeResult == 'matches' && user.livenessVerification.matchingPictures.length == 0) {
          await user.setVerificationStatus('reverifying');
        }
      } else {
        await user.setVerificationStatus('reverifying');
      }
    }
    if (shouldNewReverify(user) && index == 0) {
      if (user.verification.status === 'verified') {
        user.verification.reVerification = true;
      }
      const result = await verifyProfilePicture(user);
      if (result == 'manual') {
        await user.setVerificationStatus('reverifying');
      } else if (result == 'reject') {
        await user.setVerificationStatus('rejected');
        user.verification.rejectionReason = 'Make sure your first profile picture is a picture of you, and only you.';
      } else if (result == 'verify' && user.verification.status == 'rejected' && user.verification.reVerification) {
        await user.setVerificationStatus('verified');
        user.verification.reVerification = undefined;
      }
    }
    user.fetchNewProfileAnalysis = true;
    await user.save();
    res.json(user.originalPictures);

    await findBannedAccountKeywords(user);
    // delete embedding of the image and update merged embedding
    await userLib.handleEmbeddingUpdate(user, 'delete', req.query.id);
  }));

  router.put('/reorderPictures', asyncHandler(async (req, res, next) => {
    const { user } = req;

    user.initOriginalPictures();
    const priorFirstPicture = user.pictures[0];

    const reorderedPictures = [];
    for (const id of req.body.ids) {
      const image = user.originalPictures.find((image) => image == id);
      if (image) {
        reorderedPictures.push(image);
      }
    }
    user.originalPictures = reorderedPictures;

    await reportLib.computePictures(user);

    const firstPictureChanged = priorFirstPicture != user.pictures[0];

    await checkForUpdateToNewReverification(user);
    if (shouldNewReverify(user) && firstPictureChanged) {
      if (user.verification.status === 'verified') {
        user.verification.reVerification = true;
      }
      const result = await verifyProfilePicture(user);
      if (result == 'manual') {
        await user.setVerificationStatus('reverifying');
      } else if (result == 'reject') {
        await user.setVerificationStatus('rejected');
        user.verification.rejectionReason = 'Make sure your first profile picture is a picture of you, and only you.';
      } else if (result == 'verify' && user.verification.status == 'rejected' && user.verification.reVerification) {
        await user.setVerificationStatus('verified');
        user.verification.reVerification = undefined;
      }
    }
    const fetchNewProfileAnalysis = user.fetchNewProfileAnalysis;
    await user.save();
    await User.updateOne({ _id: user._id },{ $set: { 'fetchNewProfileAnalysis': fetchNewProfileAnalysis } });
    res.json(user.originalPictures);
  }));

  router.post('/profileVerificationPicture', s3.uploadProfileVerificationImage.single('image'), asyncHandler(async (req, res, next) => {
    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError('Image was not provided'));
    }
    console.log(req.file);
    const { user } = req;

    user.verification.pictures.push(req.file.key);
    user.verification.pictureUploadedAt = Date.now();
    user.verification.method = 'pose';
    user.verification.rejectionReason = undefined;
    user.verification.reVerification = undefined;
    if (!user.events.finished_signup) {
      user.verification.attemptedVerificationDuringSignup = true;
    }
    await user.save();

    if (!user.isConfigTrue('allow_pose_verification')) {
      // pose verification is disabled
      await userLib.handleAutomatedRejection(user, updateAppMessage);
      return res.json({});
    }

    setFaceComparisonReferenceImage(user, req.file.key);
    const faceComparisonResult = await verifyProfilePicture(user);
    await user.save();
    if (faceComparisonResult === 'reject') {
      await userLib.handleAutomatedRejection(user, 'Make sure your first profile picture is a picture of you, and only you.');
      return res.json({});
    }

    // support will manually review
    if (user.verification.status == 'unverified'
        || user.verification.status == 'rejected') {
      await user.setVerificationStatus('pending');
      await user.save();
    }

    res.json({});

    /*
    if (user.metrics.lastSeenWeb && user.gender == 'female') {
      await reportLib.createReport(
        user,
        null,
        ['Auto-report due to web pose verification'],
      );
    }

    // Shadowban non-binary new scammers
    if (moment().diff(user.createdAt, 'days') < 3 && user.metrics.lastSeenWeb && user.gender === 'non-binary' && user.verification?.method === 'pose' && !user.shadowBanned) {
      await reportLib.shadowBan(user, null, 'non-binary AI pose', null);
    }

    // max 10 attempts allowed per 24 hours
    const numPriorAttempts = await PoseVerification.countDocuments({
      user: req.uid,
      createdAt: { $gt: moment().subtract(24, 'hours').toDate() },
    });
    if (numPriorAttempts >= 10) {
      user.verification.status = 'rejected';
      user.verification.updatedAt = moment().add(1, 'day');
      await user.save();
      return;
    }

    const bannedFace = await findBannedFace(req.file.key);
    if (bannedFace) {
      await reportLib.banDueToBannedFaceFound(req.user, 'verification picture', req.file.key, bannedFace.Face.ExternalImageId);
    }

    setFaceComparisonReferenceImage(user, req.file.key);
    const faceComparisonResult = await verifyProfilePicture(user);
    await user.save();
    if (faceComparisonResult === 'reject') {
      await userLib.handleAutomatedRejection(user, 'Not same person');
      return;
    }

    const poseVerification = await openai.handlePoseVerification(user, req.file.key);
    if (poseVerification.status === 'rejected') {
      await userLib.handleAutomatedRejection(user, poseVerification.rejectionReason);
    } else if (poseVerification.status === 'verified') {
      await userLib.handleAutomatedApproval(user);
    }
    */
  }));

  router.post('/profileVerificationPicture/liveness', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { img, secure } = req.body;

    if (constants.disableYoti()) {
      // yoti verification is disabled
      await user.setVerificationStatus('rejected');
      user.verification.verifiedBy = null;
      user.verification.verifiedDate = Date.now();
      user.verification.rejectionReason = updateAppMessage;
      await user.save();
      return res.json({
        verificationStatus: user.verification.status,
        rejectionReason: translate(user.verification.rejectionReason, user.locale),
      });
    }

    if (!img) {
      return next(invalidInputError('Image was not provided'));
    }

    const { version, token, verification, signature } = secure || {};
    if (!secure || !version || cmp(version, '2.7.0') < 0 || !token || !verification || !signature) {
      return next(invalidInputError());
    }

    if (user.versionAtLeast('1.13.73')) {
      const eligible = await checkYotiVerificationEligibility(user);
      if (!eligible) {
        return next(forbiddenError());
      }
    }

    const key = await s3.uploadYotiVerificationImage(req.uid, img);
    if (!key) {
      return next(invalidInputError());
    }

    user.verification.pictures.push(key);
    user.verification.pictureUploadedAt = Date.now();
    user.verification.method = 'yoti';
    user.verification.rejectionReason = undefined;
    user.verification.reVerification = undefined;
    if (!user.events.finished_signup) {
      user.verification.attemptedVerificationDuringSignup = true;
    }
    if (user.verification.loadedYotiAt && user.verification.numSecondsToCompleteYoti === undefined) {
      user.verification.numSecondsToCompleteYoti = Math.floor((Date.now() - user.verification.loadedYotiAt) / 1000);
    }
    await user.save();

    const sendResponse = () => {
      res.json({
        verificationStatus: user.verification.status,
        rejectionReason: user.verification.rejectionReason,
      });
    };

    /*
    if (user.shadowBanned && !reportLib.eligibleForVerification(user.bannedReason)) {
      await userLib.updateVerificationStatus(user, 'rejected', 'Photo unclear', 'automated rejection due to shadow ban');
      return sendResponse();
    }
    */

    setFaceComparisonReferenceImage(user, key);
    const faceComparisonResult = await verifyProfilePicture(user);
    await user.save();
    if (faceComparisonResult === 'reject') {
      await userLib.updateVerificationStatus(user, 'rejected', 'Make sure your first profile picture is a picture of you, and only you.', 'failed face comparison');
      return sendResponse();
    }

    // This will check for yes_overlay_text in the image and reject user verification
    const moderationService = new ImageModerationService(['Hive']);
    const moderationResults = await moderationService.moderatePicture(key, false, true);
    if (moderationResults.hive_v2) {
      await userLib.updateVerificationStatus(user, 'rejected', 'Make sure your first profile picture is a picture of you, and only you.', 'yes_overlay_text detected');
      return sendResponse();
    }

    const detectionResult = await getYotiDetectionResult(req.body);
    if (detectionResult?.prediction === 'real') {
      // We will check for banned faces after the detection result
      const bannedFace = await findBannedFace(key);
      if (bannedFace) {
        await reportLib.banDueToBannedFaceFound(req.user, 'verification picture', key, bannedFace.Face.ExternalImageId);
      }
      const failedTurnstile = await RecaptchaLog.findOne({
        user: user._id,
        success: false,
        logFrom: 'turnstile',
      });
      const isSuspicious = await openai.isVerificationPhotoSuspicious(user);
      if (constants.requireManualVerificationForWeb() && (user.os == 'web' || !constants.knownEmailDomains.includes(user.emailDomain) || failedTurnstile || isSuspicious)) {
        const reasons = [];
        if (user.os == 'web') {
          reasons.push('web user');
        }
        if (!constants.knownEmailDomains.includes(user.emailDomain)) {
          reasons.push('unfamiliar email domain');
        }
        if (failedTurnstile) {
          reasons.push('failed turnstile');
        }
        if (isSuspicious) {
          reasons.push('suspicious verification photo (borders detected)');
        }
        await userLib.updateVerificationStatus(user, 'pending', undefined, reasons.join(', '));
      } else {
        await userLib.updateVerificationStatus(user, 'verified', undefined, 'passed yoti verification');
      }
      sendResponse();
      await updateUserScore(user, { verification: 1 });
      await socketLib.grantVerifyProfileAward(user);
    } else {
      await userLib.updateVerificationStatus(user, 'rejected', 'Photo unclear', 'failed yoti verification');
      sendResponse();
    }

    await YotiVerification.create({
      user: user._id,
      imageKey: key,
      imageUrl: `${constants.IMAGE_DOMAIN}${key}`,
      status: detectionResult?.prediction === 'real' ? 'verified' : 'rejected',
      rejectionReason: detectionResult?.prediction === 'real' ? undefined : 'Photo unclear',
      processingTime: detectionResult?.processingTime,
      yotiRequestId: detectionResult?.yotiRequestId,
      isError: detectionResult?.isError,
      error: detectionResult?.error,
      isMultiframe: true,
    });
  }));

  router.post('/profileVerificationPicture/liveness/eligibility', asyncHandler(async (req, res) => {
    const { user } = req;
    const eligible = await checkYotiVerificationEligibility(user);
    return res.json({ eligible });
  }));

  router.get('/dailyBoos', deprecated, (req, res, next) => next(notFoundError()));

  router.get('/dailyProfiles', /*specificRouteRateLimiter(DAILY_PROFILE_ROUTE_LIMIT, DAILY_PROFILE_BLOCK_SESSION_TIME_FOR_LIMIT, false, 1, DAILY_PROFILE_BLOCK_TIME_AFTER_LIMIT),*/ userMiddleware.checkTeleportExpiration, asyncHandler(async (req, res, next) => {
    let excludeProfileIds = req.query.excludeProfileIds

    if(excludeProfileIds && typeof(excludeProfileIds) !== 'object'){
      excludeProfileIds = [excludeProfileIds]
    }

    let { user } = req;

    if (user.exclusionListFailed) {
      return next(forbiddenError('Something went wrong. Please try again.'));
    }

    await user.resetCurrentDayMetricsIfNeeded();

    if (excludeProfileIds && excludeProfileIds.length > 0 && !isPremium(user)) {
      user.currentDayMetrics.pendingSwipes = excludeProfileIds;
      await user.save();
    }

    if (user.hidden) {
      return next(invalidInputError('To see others, make yourself visible on boo.'));
    }

    let dailyLimit = constants.getDailyProfileLimit();

    if (!isPremium(user) && !limitLikes(user)) {
      dailyLimit = Math.min(dailyLimit, getRemainingDailyLimit(user));
      if (dailyLimit <= 0 && !showTopProfiles(user)) {
        console.log('Already reached max daily boo limit. user: ', user._id);
        user.currentDayMetrics.hitSwipeLimit = true;
        await user.save();
        return res.json({
          profiles: [],
          dailyLimitExceeded: true,
          dailyLimitResetsAt: user.metrics.swipeLimitResetTime,
          numSwipesRemaining: getRemainingDailyLimit(user),
        });
      }
    }

    let sortBest;
    let sortBestForNewUserPercent;
    if (showTopProfiles(user) && (dailyLimit <= 0 || user.pictures.length <= 0)) {
      sortBest = true;
      dailyLimit = Math.ceil(constants.DAILY_PROFILE_LIMIT / 3);
    }
    else if (user.isConfigTrue('w1_show_best')) {
      const day = moment().diff(user.createdAt, 'days');
      if (day <= 7) {
        sortBestForNewUserPercent = 1 - day/8;
      }
    }
    let profiles = await profilesLib.getProfiles(
      user,
      dailyLimit,
      sortBest,
      false,
      sortBestForNewUserPercent,
    );

    /*
    if (
      user.isConfigTrue('automatic_revival_local_profiles')
      && !user.metrics.automatic_revival_local_profiles
      && user.preferences.local
      && (profiles.length == 0 || !profiles[0].nearby)
    ) {
      // automatic revival
      await actionLib.removePassesFromUser(user._id);

      user.preferencesModifiedAt = new Date();  // reset cache
      user.metrics.automatic_revival_local_profiles = 1;
      user.metrics.numLocalLikesSentBeforeRevival = user.metrics.numLocalLikesSent;
      user.metrics.numLocalPassesSentBeforeRevival = user.metrics.numLocalPassesSent;
      user.metrics.numLocalSwipesSentBeforeRevival = user.metrics.numLocalLikesSent + user.metrics.numLocalPassesSent;
      user.metrics.daysOnPlatformBeforeRevival = Math.abs(moment().diff(user.createdAt, 'days'));
      user.metrics.automaticRevivalDate = new Date();
      await user.save();

      // refresh profiles
      profiles = await profilesLib.getProfiles(
        user,
        dailyLimit,
        sortBest,
        false,
        sortBestForNewUserPercent,
      );
    }
    */

    if (profiles.length == 0 && sortBest) {
      await ExclusionList.updateOne({ user: user._id }, { $set: { topProfilesExclusionList: [] }})
      profiles = await profilesLib.getProfiles(
        user,
        dailyLimit,
        sortBest,
        false,
        sortBestForNewUserPercent,
      );
    }

    if (profiles.length == 0) {
      return res.json({
        profiles: [],
        noUsersNearby: true,
        numSwipesRemaining: getRemainingDailyLimit(user),
      });
    }

    if (sortBest) {
      await addToTopProfilesExclusionList(user._id, profiles[0]._id);
      if (user.minDistanceForCarrotAlgo) {
        user.metrics.displayedCarrotProfilesWithMinDistance = true;
      }
      user.metrics.displayedCarrotProfiles = true;
    } else {
      user.metrics.displayedCarrotProfiles = false;
    }
    await user.save();

    res.json({
      profiles,
      numSwipesRemaining: getRemainingDailyLimit(user),
    });

    if (!sortBest && user.recentRecommendations.length < constants.DAILY_PROFILE_LIMIT * 2) {
      // prefetch next batch in the background
      user = await User.findById(user._id);
      await profilesLib.getProfiles(
        user,
        constants.DAILY_PROFILE_LIMIT * 2,
        false,
        true,
      );
    }
  }));

  router.get('/topPicks', userMiddleware.checkTeleportExpiration, asyncHandler(async (req, res, next) => {
    let { user } = req;

    if (user.exclusionListFailed) {
      return next(forbiddenError('Something went wrong. Please try again.'));
    }

    if (user.hidden) {
      return next(invalidInputError('To see others, make yourself visible on boo.'));
    }

    await user.resetCurrentDayMetricsIfNeeded();
    let profiles = await profilesLib.getTopPicks(
      user,
    );

    let rv = {
      profiles,
      alreadySuperLiked: user.currentDayMetrics.topPicksSuperLiked,
    };

    if (profiles.length == 0) {
      rv.noUsersNearby = true;
    }

    res.json(rv);
  }));

  router.get('/rewind', findUser, asyncHandler(findUserMetadata), asyncHandler(async (req, res, next) => {
    const { user } = req;
    const profileId = req.query.profileId

    let price = 0;

    // if not premium, then need to spend coins
    if (!isPremium(user)) {
      if (req.query.price === undefined || req.query.price === null) {
        return next(forbiddenError('Rewind is only available to premium users.'));
      }

      if (req.query.price != coinsConstants.rewindCost) {
        return next(conflictError('Your Boo Infinity subscription has expired. If you have renewed your subscription, please restart the app to refresh your benefits.'));
      }

      price = req.query.price;
      if (req.userMetadata.coins < price) {
        return next(forbiddenError('Insufficient coins'));
      }
    }

    if (user.hidden) {
      return next(invalidInputError('To see others, make yourself visible on boo.'));
    }

    const errMsg = 'The Boo multiverse laws of space-time only allow time travel back to souls you passed on.';

    if (!user.metrics.previousAction) {
      return res.json({ message: errMsg });
    }

    let action;
    let profile;
    //if profileId exist
    if (profileId){
      profile = await User.findById(profileId);
      if (!profile) {
        return res.json({ message: errMsg });
      }
    } else {
      action = await Action.findById(user.metrics.previousAction);
      if (!action) {
        return res.json({ message: errMsg });
      }
      profile = await User.findById(action.to);
      if (!profile) {
        return res.json({ message: errMsg });
      }
    }

    const block = await Block.findOne({ from: user._id, to: profile._id });
    if (block) {
      return res.json({ message: errMsg });
    }
    const formattedProfile = chatLib.formatProfile(profile, user, {nearby:true});

    if (profile._id == req.userMetadata.priorRewind) {
      // if same as prior rewind, don't deduct coins
      return res.json({
        user: formattedProfile,
        coinsRemaining: req.userMetadata.coins,
      });
    }

    // deduct coins
    const coins = await coinsLib.updateCoins(
      { user: user._id },
      {
        $inc: {
          coins: -1 * price,
        },
        $set: {
          priorRewind: profile._id,
        },
      },
      'rewind',
    );

    user.metrics.numRewindUsed += 1;
    user.updateEvents({ use_time_travel: true })
    await user.save();

    return res.json({
      user: formattedProfile,
      coinsRemaining: coins,
    });
  }));

  router.patch('/sendLike', findUser, asyncHandler(findOtherUser), asyncHandler(verifyNotBlocked), asyncHandler(async (req, res, next) => {
    const { user, otherUser, } = req;

    if (user._id == otherUser._id) {
      return next(invalidInputError());
    }

    if (!isPremium(user)) {
      if (getRemainingDailyLimit(user) <= 0) {
        user.currentDayMetrics.hitSwipeLimit = true;
        await user.save();
        return res.json({
          dailyLimitExceeded: true,
          dailyLimitResetsAt: user.metrics.swipeLimitResetTime,
          numSwipesRemaining: getRemainingDailyLimit(user),
        });
      }
    }

    const action = await onLikeSentPreResponse(user, otherUser);

    let chat = await Chat.findDirectChat(user._id, otherUser._id, true);

    // if pending chat already exists
    if (chat && chat.pendingUser == otherUser._id) {
      const threeMonthsAgo = new Date(Date.now() - 3 * 30 * 24 * 60 * 60 * 1000) // 3 months before
      // if chat was created less than 3 months ago, then this is a duplicate like, so return early
      if (threeMonthsAgo < chat.createdAt) {
        return res.json({
          numSwipesRemaining: getRemainingDailyLimit(user),
        });
      }
      // otherwise, this is a like sent to a repeated swiping profile, so reset the chat
      chat.lastMessageTime = Date.now()
      chatLib.incrementUnreadMessages(chat, otherUser._id);
      await chat.save()
    }

    // if chat does not exist, create pending chat
    if (!chat) {
      chat = new Chat({
        users: [user, otherUser],
        createdBy: user._id,
        pendingUser: req.body.user,
        lastMessageTime: Date.now(),
      });
      chat.readReceipts.set(user._id, { numUnreadMessages: 0 });
      chat.readReceipts.set(req.body.user, { numUnreadMessages: 1, ...(otherUser.isBoostActive() && { matchIndicator: 'boost' }) });
      if (user.shadowBanned) {
        chat.bannedUsers = [user._id];
      }
      await chat.save();

      await UsersWhoLiked.addUserWhoLiked(otherUser, user);

      await onNewPendingLike(user, otherUser, chat);
    }

    // updateMetrics for this user
    {
      const start = new Date().getTime();

      await user.resetCurrentDayMetricsIfNeeded();
      const local = isLocal(user, otherUser);
      const detailedMetric = local ? 'numLocalLikesSent' : 'numGlobalLikesSent';
      const metricsToUpdate = [
        'numLikesSent',
        detailedMetric,
        'numActionsSent',

        // legacy
        'numActionsCurrentDay',
        'numLikesSentCurrentDay',
      ];

      const daysOnPlatform = Math.abs(moment().diff(user.createdAt, 'days'));
      if (daysOnPlatform == 0) {
        metricsToUpdate.push('numActionsSentD0');
        metricsToUpdate.push('numLikesSentD0');
      }

      await User.incrementMetrics(user._id, metricsToUpdate);

      if (!isPremium(user)) {
        await User.updateOne(
          { _id: user. _id },
          {
            $addToSet: { 'currentDayMetrics.swipes': otherUser._id },
            $pull: { 'currentDayMetrics.pendingSwipes': otherUser._id },
          },
        );
      }

      const end = new Date().getTime();
      console.log(`User ${user._id} send like, time to update metrics for this user: ${end-start} ms`);
    }

    if (!otherUser.versionAtLeast('1.13.48')) {
      await addToExclusionList(otherUser._id, user._id);
    }

    user.metrics.previousAction = action;
    await user.save();

    let showSecretAdmirer;
    if (
      user.isConfigTrue('app_165')
      && user.currentDayMetrics.showSecretAdmirerOnSwipe > 0
      && user.currentDayMetrics.numActionsSent + 1 >= user.currentDayMetrics.showSecretAdmirerOnSwipe
    ) {
      const daysOnPlatform = user.getDaysOnPlatform();
      if (!user.metrics.receivedSecretAdmirerOnDay.includes(daysOnPlatform)) {
        showSecretAdmirer = true;
        user.currentDayMetrics.showSecretAdmirerOnSwipe = null;
        user.metrics.receivedSecretAdmirerOnDay.push(daysOnPlatform);
        await user.save();
      }
    }

    // if chat pending on this user, then approve chat
    if (chat.pendingUser == user._id) {
      await approveChat(chat, user, otherUser, res.io);
      let approvedChat = chatLib.formatChat(
        chat.toObject({ flattenMaps: true }),
        user,
      );
      return res.json({
        approvedChat,
        showSecretAdmirer,
        numSwipesRemaining: getRemainingDailyLimit(user),
      });
    }

    if (!process.env.TESTING) {
      res.json({
        showSecretAdmirer,
        numSwipesRemaining: getRemainingDailyLimit(user),
      });
    }

    if (!user.shadowBanned && user.metrics.numPendingReports == 0) {
      // save follow
      {
        const start = new Date().getTime();

        await Follow.autoFollowLike(user, otherUser);

        const end = new Date().getTime();
        console.log(`User ${user._id} send like, time to save follow: ${end-start} ms`);
      }

      // send socket event and notification to other user
      {
        const start = new Date().getTime();

        const formattedChat = chatLib.formatChat(
          chat.toObject({ flattenMaps: true }),
          otherUser,
        );

        sendSocketEvent(otherUser._id, 'pending chat', formattedChat);


        let analyticsLabel = 'new-love';
        let notificationTitle = 'New love!'
        let notificationBody = '%s sent you love.'

        notificationTitle = translate(notificationTitle, otherUser.locale, user.firstName);
        notificationBody = translate(
          notificationBody,
          otherUser.locale,
          user.firstName,
        );

        let preferences = genderPreferenceLib.getMutualPreferences(user, otherUser);
        if (!preferences.includes('dating')) {
          const locale = otherUser.locale;

          analyticsLabel = 'new-friend-request';
          notificationTitle = 'New friend request!';
          notificationBody = '{{name}} sent you a friend request.';
          notificationBody = translate(
            notificationBody,
            locale,
            { name: user.firstName }
          );

          notificationTitle = translate(notificationTitle, locale,  { name: user.firstName });

          // const englishTitle = translate(originalTitle, 'en');
          // const translatedTitle = translate(originalTitle, locale);

          // const englishBody = translate(originalBody, 'en', { name: user.firstName });
          // const translatedBody = translate(originalBody, locale, { name: user.firstName });

          // if (locale == 'en' || (translatedTitle != englishTitle && translatedBody != englishBody)) {
          //   notificationTitle = translatedTitle;
          //   notificationBody = translatedBody;
          //   analyticsLabel = 'new-friend-request';
          // }
        }

        const data = {
          _id: chat._id,
        };

        admin.sendNotification(
          otherUser,
          'profileLikes',
          notificationTitle,
          notificationBody,
          { pendingChat: JSON.stringify(data) },
          null,
          'love',
          analyticsLabel,
        );

        const end = new Date().getTime();
        console.log(`User ${user._id} send like, time to notify other user: ${end-start} ms`);
      }
    }

    if (process.env.TESTING) {
      // for test environments, return at end
      res.json({
        showSecretAdmirer,
        numSwipesRemaining: getRemainingDailyLimit(user),
      });
    }
  }));

  router.patch('/sendDirectMessage', userMiddleware.checkVerified, findUser, asyncHandler(findUserMetadata), asyncHandler(findOtherUser), asyncHandler(verifyNotBlocked), asyncHandler(async (req, res, next) => {
    const { user, otherUser } = req;
    const {quotedQuestion, quotedComment, quotedStory} = req.body;

    const quoteFields = {
      quotedQuestion,
      quotedComment,
      quotedStory,
    };

    const {quoteField, quoteValue } = prepareQuotedField(quoteFields)

    if (user._id == otherUser._id) {
      return next(invalidInputError());
    }

    if (!req.body.message && !req.body.gif) {
      return next(invalidInputError());
    }

    if(req.body.gif){
      if (!isValidGif(req.body.gif)) {
        return next(invalidInputError());
      }
    }

    if (req.body.message && req.body.message.length > 10000) {
      return next(invalidInputError('Character limit exceeded'));
    }

    let price = 0;

    // if not premium, then need to spend coins
    if (!(isPremium(user) || user.unlimitedDmsExpiration > new Date())) {
      if (!req.body.hasOwnProperty('price')) {
        return next(forbiddenError('Direct message is only available to premium users.'));
      }

      if (req.body.price != coinsConstants.directMessageCost
          && req.body.price != coinsConstants.directMessageCost_v2
          && req.body.price != coinsConstants.directMessageCost_v3) {
        return next(conflictError('Your Boo Infinity subscription has expired. If you have renewed your subscription, please restart the app to refresh your benefits.'));
      }

      price = req.body.price;
      if (req.userMetadata.coins < price) {
        return next(forbiddenError('Insufficient coins'));
      }
    }

    await onLikeSentPreResponse(user, otherUser);

    await addToExclusionList(otherUser._id, user._id);

    // if chat does not exist, create pending chat
    let chat = await Chat.findDirectChat(user._id, otherUser._id, true);
    if (!chat) {
      chat = new Chat({
        users: [user, otherUser],
        createdBy: user._id,
        pendingUser: otherUser._id,
        initiatedByDM: true,
      });
      if (user.shadowBanned) {
        chat.bannedUsers = [user._id];
      }
      await onNewPendingLike(user, otherUser, chat);
    }

    // if chat pending on this user, then approve chat
    let newlyApprovedChat = false;
    if (chat.pendingUser == user._id) {
      newlyApprovedChat = true;
      await approveChat(chat, user, otherUser, res.io);
    }

    let savedMessage;
    if (req.body.gif) {
      // save the message
      const message = new Message({
        chat: chat._id,
        gif: req.body.gif,
        sender: req.uid,
      });

      if (req.body.aspectRatio && req.body.aspectRatio !== null) {
        message.aspectRatio = req.body.aspectRatio;
      }

      savedMessage = await saveMessage(message);

      // update the chat's last message
      chat.lastMessage = savedMessage;
      chat.lastMessageTime = Date.now();
      chat.incrementNumMessages();
      chatLib.incrementUnreadMessages(chat, otherUser._id);
    }

    if (req.body.message) {
      // save the message
      const message = new Message({
        chat: chat._id,
        text: req.body.message,
        sender: req.uid,
      });

      savedMessage = await saveMessage(message, {quoteField, quoteValue});

      // update the chat's last message
      chat.lastMessage = savedMessage;
      chat.lastMessageTime = Date.now();
      chat.incrementNumMessages();
      chatLib.incrementUnreadMessages(chat, otherUser._id);
    }

    if (chat.pendingUser && otherUser.isBoostActive()) {
      const readReceipt = chat.readReceipts?.get(otherUser._id);
      if (readReceipt) {
        readReceipt.matchIndicator = 'boost';
        chat.readReceipts.set(otherUser._id, readReceipt);
      } else {
        chat.readReceipts.set(otherUser._id, { numUnreadMessages: 1, matchIndicator: 'boost' });
      }
    }
    chat.rejected = undefined
    const savedChat = await chat.save();

    // if chat is already approved or newly approved, handle DM as message
    if (!chat.pendingUser) {
      const notifText = savedMessage.text;
      if (!newlyApprovedChat) {
        // for newly approved chats, we only send the 'approved chat' socket event
        // sending the 'message' socket event will result in duplicate notifications
        sendMessageNotifications(user, chat, savedMessage, notifText, res.io);
      }
      let approvedChat;
      if (newlyApprovedChat) {
        approvedChat = chatLib.formatChat(
          chat.toObject({ flattenMaps: true }),
          user,
        );
      }
      return res.json({
        coinsRemaining: req.userMetadata.coins,
        approvedChat,
      });
    }

    // deduct coins
    const coins = await coinsLib.updateCoins(
      { user: user._id },
      {
        $inc: {
          coins: -1 * price,
          directMessagesSent: 1,
          coinsSpentOnDirectMessages: price,
        },
      },
      'send direct message',
    );

    if (quotedQuestion || quotedComment) {
      user.metrics.numDMSentFromSocial += 1;
    } else if (quotedStory) {
      user.metrics.numDMSentFromStories += 1;
    } else {
      user.metrics.numDMSentFromSwiping += 1;
    }
    user.metrics.numDMSent += 1;
    user.updateEvents({ use_dm: true })
    await user.save();

    if (!isPremium(user)) {
      await User.updateOne(
        { _id: user. _id },
        {
          $pull: { 'currentDayMetrics.pendingSwipes': otherUser._id },
        },
      );
    }

    socketLib.sendCoinRewards(
      user._id,
      await coinsLib.onSendDM(user),
    );

    otherUser.metrics.numDMReceived += 1;
    await otherUser.save();

    if (!user.shadowBanned && user.metrics.numPendingReports == 0) {
      await Follow.autoFollowLike(user, otherUser);

      // send socket event and notification to other user
      const formattedChat = chatLib.formatChat(
        savedChat.toObject({ flattenMaps: true }),
        otherUser,
      );

      sendSocketEvent(otherUser._id, 'pending chat', formattedChat);

      let analyticsLabel = 'new-love-dm';
      let notificationTitle = 'New love!'
      let notificationBody = '%s sent you a message.'

      let title = translate(notificationTitle, otherUser.locale);
      let body = translate(
        notificationBody,
        otherUser.locale,
        user.firstName,
      );


      let preferences = genderPreferenceLib.getMutualPreferences(user, otherUser);
      if (!preferences.includes('dating')) {
        const locale = otherUser.locale;
        notificationTitle = 'New friend request!';
        analyticsLabel = 'new-friend-request-dm';

        title = translate(notificationTitle, locale);

      }

      if (quotedStory) {
        title = translate('%s replied to your story!', otherUser.locale, user.firstName);
        body = savedMessage.text;
        analyticsLabel = 'replied-to-your-story';
      }
      const data = {
        _id: savedChat._id,
      };
      admin.sendNotification(
        otherUser,
        'profileLikes',
        title,
        body,
        { pendingChat: JSON.stringify(data) },
        null,
        'love',
        analyticsLabel,
      );
    }

    return res.json({
      coinsRemaining: coins,
    });
  }));

  router.patch('/sendSuperLike', findUser, userMiddleware.checkVerified, asyncHandler(findOtherUser), asyncHandler(verifyNotBlocked), asyncHandler(async (req, res, next) => {
    const { user, otherUser } = req;

    if (user._id == otherUser._id) {
      return next(invalidInputError());
    }

    if (user.numSuperLikes + user.numSuperLikesFree <= 0) {
      return next(forbiddenError());
    }

    if(req.body.gif){
      if (!isValidGif(req.body.gif)) {
        return next(invalidInputError());
      }
    }

    await onLikeSentPreResponse(user, otherUser);

    await addToExclusionList(otherUser._id, user._id);

    // if chat does not exist, create pending chat
    let chat = await Chat.findDirectChat(user._id, otherUser._id, true);
    if (!chat) {
      chat = new Chat({
        users: [user, otherUser],
        createdBy: user._id,
        pendingUser: otherUser._id,
        initiatedBySuperLike: true,
      });
      if (user.shadowBanned) {
        chat.bannedUsers = [user._id];
      }
      await onNewPendingLike(user, otherUser, chat);
    }

    // if chat pending on this user, then approve chat
    let newlyApprovedChat = false;
    if (chat.pendingUser == user._id) {
      newlyApprovedChat = true;
      await approveChat(chat, user, otherUser, res.io);
    }

    let savedMessage;

    if (req.body.gif) {
      // save the message
      const message = new Message({
        chat: chat._id,
        gif: req.body.gif,
        sender: req.uid,
      });

      if (req.body.aspectRatio && req.body.aspectRatio !== null) {
        message.aspectRatio = req.body.aspectRatio;
      }

      savedMessage = await saveMessage(message);

      // update the chat's last message
      chat.lastMessage = savedMessage;
      chat.lastMessageTime = Date.now();
      chat.incrementNumMessages();
      chatLib.incrementUnreadMessages(chat, otherUser._id);
    }

    // save the message
    if (req.body.message) {
      const message = new Message({
        chat: chat._id,
        text: req.body.message,
        sender: req.uid,
      });

      savedMessage = await saveMessage(message);

      // update the chat's last message
      chat.lastMessage = savedMessage;
      chat.lastMessageTime = Date.now();
      chat.incrementNumMessages();
      chatLib.incrementUnreadMessages(chat, otherUser._id);
    }

    // reactivate chat and mark as super like
    chat.lastMessageTime = Date.now();
    chat.initiatedBySuperLike = true;

    // Add match indicator to super like
    const readReceipt = chat.readReceipts?.get(user._id);
    if (readReceipt) {
      readReceipt.matchIndicator = 'superLike';
      chat.readReceipts.set(user._id, readReceipt);
    } else {
      chat.readReceipts.set(user._id, { numUnreadMessages: 0, matchIndicator: 'superLike' });
    }
    if (chat.pendingUser && otherUser.isBoostActive()) {
      const otherReadReceipt = chat.readReceipts?.get(otherUser._id);
      if (otherReadReceipt) {
        otherReadReceipt.matchIndicator = 'boost';
        chat.readReceipts.set(otherUser._id, otherReadReceipt);
      } else {
        chat.readReceipts.set(otherUser._id, { numUnreadMessages: 1, matchIndicator: 'boost' });
      }
    }
    chat.rejected = undefined
    await chat.save();

    // check if user is a top pick
    if (user.currentDayMetrics.topPicks.includes(otherUser._id)) {
      user.currentDayMetrics.topPicksSuperLiked.push(otherUser._id);
    }

    // deduct super likes
    if (user.numSuperLikesFree > 0) {
      user.numSuperLikesFree -= 1;
      user.metrics.numSuperLikesSentFree += 1;
      await SuperLikeTransaction.create({
        user: user._id,
        freeSuperLoveTransactionAmount: -1,
        freeSuperLoveNewBalance: user.numSuperLikesFree,
        paidSuperLoveTransactionAmount: 0,
        paidSuperLoveNewBalance: user.numSuperLikes,
        description: 'used free super love',
      });
    } else {
      user.numSuperLikes -= 1;
      await SuperLikeTransaction.create({
        user: user._id,
        freeSuperLoveTransactionAmount: 0,
        freeSuperLoveNewBalance: user.numSuperLikesFree,
        paidSuperLoveTransactionAmount: -1,
        paidSuperLoveNewBalance: user.numSuperLikes,
        description: 'used paid super love',
      });
    }
    user.metrics.numSuperLikesSent += 1;
    user.updateEvents({ use_superlove: true })
    await user.save();

    if (!isPremium(user)) {
      await User.updateOne(
        { _id: user. _id },
        {
          $pull: { 'currentDayMetrics.pendingSwipes': otherUser._id },
        },
      );
    }

    // if chat is newly approved, return early
    if (newlyApprovedChat) {
      let approvedChat = chatLib.formatChat(
        chat.toObject({ flattenMaps: true }),
        user,
      );
      return res.json({
        approvedChat,
      });
    }

    if (!user.shadowBanned && user.metrics.numPendingReports == 0) {
      await Follow.autoFollowLike(user, otherUser);

      // send socket event and notification to other user
      const formattedChat = chatLib.formatChat(
        chat.toObject({ flattenMaps: true }),
        otherUser,
      );

      sendSocketEvent(otherUser._id, 'pending chat', formattedChat);

      let analyticsLabel = 'new-super-love';
      let notificationTitle = 'New super love!'
      let notificationBody = '%s sent you a super love.'

      notificationBody = translate(
        notificationBody,
        otherUser.locale,
        user.firstName,
      );

      notificationTitle = translate(notificationTitle, otherUser.locale)

      const data = {
        _id: chat._id,
      };
      admin.sendNotification(
        otherUser,
        'profileLikes',
        notificationTitle,
        notificationBody,
        { pendingChat: JSON.stringify(data) },
        null,
        'love',
        analyticsLabel,
      );
    }

    return res.json({});
  }));

  router.patch('/sendGift', deprecated, (req, res, next) => next(notFoundError()));

  router.patch('/like', deprecated, (req, res, next) => next(notFoundError()));

  router.patch('/pass', findUser, asyncHandler(findOtherUser), asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { otherUser } = req;
    if (user._id == otherUser._id) {
      return next(invalidInputError());
    }

    if (!isPremium(user) && !limitLikes(user)) {
      if (getRemainingDailyLimit(user) <= 0) {
        user.currentDayMetrics.hitSwipeLimit = true;
        await user.save();
        return res.json({
          dailyLimitExceeded: true,
          dailyLimitResetsAt: user.metrics.swipeLimitResetTime,
          numSwipesRemaining: getRemainingDailyLimit(user),
        });
      }
    }

    // add to exclusion list
    {
      const start = new Date().getTime();
      await addToExclusionList(user._id, req.body.user);

      const end = new Date().getTime();
      console.log(`User ${user._id} pass, time to update exclusion list: ${end-start} ms`);
    }

    // save the pass
    let action;
    {
      const start = new Date().getTime();

      action = await Action.findOne({ from: user._id, to: req.body.user });
      const threeMonthsAgo = new Date(Date.now() - 3 * 30 * 24 * 60 * 60 * 1000) // 3 months before
      if (action && action.createdAt && action.createdAt < threeMonthsAgo) {
        action.createdAt = Date.now();
        action.pass = false;
      }
      if (!action) {
        action = new Action({
          from: user._id,
          to: req.body.user,
        });
      }

      if (action.pass) {
        return res.json({
          numSwipesRemaining: getRemainingDailyLimit(user),
        });
      }
      action.pass = true;
      await action.save();

      const end = new Date().getTime();
      console.log(`User ${user._id} pass, time to save action: ${end-start} ms`);
    }

    // updateMetrics for this user
    let local;
    {
      const start = new Date().getTime();

      await user.resetCurrentDayMetricsIfNeeded();
      local = isLocal(user, otherUser);
      const detailedMetric = local ? 'numLocalPassesSent' : 'numGlobalPassesSent';
      const metricsToUpdate = [
        'numPassesSent',
        detailedMetric,
        'numActionsSent',

        // legacy
        'numActionsCurrentDay',
      ];

      const daysOnPlatform = Math.abs(moment().diff(user.createdAt, 'days'));
      if (daysOnPlatform == 0) {
        metricsToUpdate.push('numActionsSentD0');
      }

      const tags = profilesLib.getProfileTags(user,otherUser)
      metricsToUpdate.push(...profilesLib.getMetricTags(tags))

      await User.incrementMetrics(user._id, metricsToUpdate);

      if (!isPremium(user)) {
        await User.updateOne(
          { _id: user. _id },
          {
            $addToSet: { 'currentDayMetrics.swipes': otherUser._id },
            $pull: { 'currentDayMetrics.pendingSwipes': otherUser._id },
          },
        );
      }

      user.metrics.previousAction = action;
      await user.save();

      const end = new Date().getTime();
      console.log(`User ${user._id} pass, time to update metrics for this user: ${end-start} ms`);
    }

    // check if potential match
    let missedPotentialMatch;
    let chat = await Chat.findDirectChat(user._id, otherUser._id);
    if (chat && chat.pendingUser == user._id) {
      missedPotentialMatch = true;
    }

    let showSecretAdmirer;
    if (
      user.isConfigTrue('app_165')
      && user.currentDayMetrics.showSecretAdmirerOnSwipe > 0
      && user.currentDayMetrics.numActionsSent + 1 >= user.currentDayMetrics.showSecretAdmirerOnSwipe
    ) {
      const daysOnPlatform = user.getDaysOnPlatform();
      if (!user.metrics.receivedSecretAdmirerOnDay.includes(daysOnPlatform)) {
        showSecretAdmirer = true;
        user.currentDayMetrics.showSecretAdmirerOnSwipe = null;
        user.metrics.receivedSecretAdmirerOnDay.push(daysOnPlatform);
        await user.save();
      }
    }

    if (!process.env.TESTING) {
      res.json({
        missedPotentialMatch,
        showSecretAdmirer,
        numSwipesRemaining: getRemainingDailyLimit(user),
      });
    }

    // save profile view
    {
      const start = new Date().getTime();

      await onProfileView(user, otherUser, true);

      const end = new Date().getTime();
      console.log(`User ${user._id} pass, time to save profile view: ${end-start} ms`);
    }

    // update metrics for other user
    {
      const start = new Date().getTime();

      await otherUser.resetCurrentDayMetricsIfNeeded();
      await User.incrementMetrics(otherUser._id, [
        'numPassesReceived',
        'numActionsReceived',

        // legacy
        'numPassesReceivedCurrentDay',
      ]);
      if(otherUser.isBoostActive()){
        await User.incrementMetric(otherUser._id,'numActionsReceivedDuringBoostQuota',-1)
      }
      await updateUserScore(otherUser, { actions: 1 });

      if (otherUser.isBoostActive()) {
        const metrics = ['numActionsReceived'];
        if (local) {
          metrics.push('numLocalActionsReceived');
        }
        await BoostMetric.incrementMetrics(otherUser._id, metrics);
      }
      else if (otherUser.isPostBoostPeriod()) {
        const metrics = ['postBoostNumActionsReceived'];
        await BoostMetric.incrementMetrics(otherUser._id, metrics);
      }

      const end = new Date().getTime();
      console.log(`User ${user._id} pass, time to update metrics for other user: ${end-start} ms`);
    }

    if (process.env.TESTING) {
      // for test environments, return at end
      res.json({
        missedPotentialMatch,
        showSecretAdmirer,
        numSwipesRemaining: getRemainingDailyLimit(user),
      });
    }
  }));

  router.patch('/approve', asyncHandler(findPendingChatPopulate), asyncHandler(async (req, res, next) => {
    const { chat } = req;
    const { user } = req;
    const otherUser = req.chat.users.find((user) => user._id != req.uid);

    await approveChat(chat, user, otherUser, res.io);
    await addToExclusionList(user._id, otherUser._id);
    return res.json({});
  }));

  router.patch('/reject', asyncHandler(findPendingChat), asyncHandler(async (req, res, next) => {
    // update metrics
    const { chat } = req;
    const otherUserId = chat.users.find((id) => id != req.uid);
    await User.incrementMetrics(req.uid, ['numRejectionsSent']);
    await User.incrementMetric(req.uid, 'numPendingLikes', -1);
    await User.incrementMetrics(otherUserId, ['numRejectionsReceived']);

    // reject the chat
    chat.rejected = true
    await chat.save()

    // remove the likes
    await Action.updateOne(
      { from: req.uid, to: otherUserId },
      { like: false },
    );
    await Action.updateOne(
      { from: otherUserId, to: req.uid },
      { like: false },
    );

    return res.json({});
  }));

  router.patch('/unmatch', asyncHandler(findChat), asyncHandler(async (req, res, next) => {
    // update metrics
    const { chat } = req;
    const otherUserId = chat.users.find((id) => id != req.uid);
    await User.incrementMetrics(req.uid, ['numUnmatchesSent']);
    await User.incrementMetrics(otherUserId, ['numUnmatchesReceived']);

    if (chat.pendingUser) {
      await User.incrementMetric(chat.pendingUser, 'numPendingLikes', -1);
    }

    // delete the chat
    await Chat.removeChatAndMessages(chat);
    console.log(`Removed chat ${chat}`);

    sendSocketEvent(otherUserId, 'deleted chat', { _id: chat._id });

    // remove the likes
    await Action.updateOne(
      { from: req.uid, to: otherUserId },
      { like: false },
    );
    await Action.updateOne(
      { from: otherUserId, to: req.uid },
      { like: false },
    );

    // remove follows
    await Follow.removeFollow(req.uid, otherUserId);
    await Follow.removeFollow(otherUserId, req.uid);

    // remove from friend list
    await FriendList.removeFriendship(req.uid, otherUserId);

    await checkForDeleteInstantMatch(chat);
    return res.json({});
  }));

  router.patch('/block', findUser, asyncHandler(findOtherUser), asyncHandler(async (req, res, next) => {
    if (req.user._id === req.otherUser._id) {
      console.log(`user: ${req.user._id}tried blocking self`);
      return next(applicationError()); // to throw error if user tries blocking self
    }

    if (await UnBlocked.recentlyUnblocked(req.user._id, req.otherUser._id)) {
      return next(forbiddenError(req.__('You recently unblocked the user. To avoid abuse, you will not be able to block them again for 7 days.')));
    }

    if (req.otherUser.deviceId) {
      await blockUsersWithDeviceId(req.user._id, req.otherUser, res.io);
    } else {
      await blockUser(req.user._id, req.otherUser._id);
    }

    if (req.user.currentDayMetrics.topPicks.includes(req.otherUser._id)) {
      await User.updateOne(
        {
          _id: req.user._id,
        },
        {
          $pull: { 'currentDayMetrics.topPicks': req.otherUser._id },
        },
      );
    }

    return res.json({});
  }));

  router.patch('/hideContacts', findUser, validateEmailPhoneNumbers, asyncHandler(async (req, res, next) => {
    // add data to hideList
    if (req.body.reset) await unHideUsers(req.user._id);
    else await hideUsers(req.user._id, res.io, req.otherEmails, req.otherNumbers);
    return res.json({});
  }));

  router.put('/hideFromKeywords', asyncHandler(async (req, res, next) => {
    req.user.hideFromKeywords = req.body.hideFromKeywords.map(x => x.toLowerCase().trim());
    await req.user.save();
    return res.json({});
  }));

  router.put('/hideFromNearby', asyncHandler(async (req, res, next) => {
    req.user.hideFromNearby = req.body.hideFromNearby;
    await req.user.save();
    return res.json({});
  }));

  router.patch('/unmatchRelationship', deprecated, (req, res, next) => next(notFoundError()));

  router.get('/profileDetails', findUser, asyncHandler(async (req, res, next) => {
    const user = req.user;

    const userId = req.query.user;
    const handle = req.query.handle;
    if (!userId && !handle) {
      return next(invalidInputError());
    }
    if (userId && typeof userId !== 'string') {
      return next(invalidInputError());
    }
    if (handle && typeof handle !== 'string') {
      return next(invalidInputError());
    }
    let otherUser;
    if (userId) {
      otherUser = await User.findById(userId);
    } else if (handle) {
      otherUser = await User.findOne({handle});
    }
    if (!otherUser) {
      return next(notFoundError());
    }
    let otherUserId = otherUser._id;

    // get follow status
    const follow = await Follow.findOne({
      from: req.uid,
      to: otherUserId,
    });

    let followRequestApproved = null;
    if (follow) {
      followRequestApproved = follow.approved;
    }

    // get matched status
    const chat = await Chat.findDirectChat(req.uid, otherUserId);
    const isMatched = !!(chat && !chat.pendingUser && !(otherUser.banned || otherUser.shadowBanned));
    const isChatExpired = !!(chat && chatLib.isChatExpired(chat));

    const dndPost = (chat && chat.dndPostFrom) ? chat.dndPostFrom.filter(user => req.uid != user).length > 0 : false;
    const dndMessage = (chat && chat.dndMessageFrom) ? chat.dndMessageFrom.filter(user => req.uid != user).length > 0 : false;

    // get stories
    let stories;
    if (isMatched || req.user.admin) {
      const profilesWithStories = await getProfilesWithStories({
        user: req.user,
        createdBy: otherUserId,
        visibility: { $in: ['everyone', 'followersAndFriends', 'friends'] },
      });
      stories = profilesWithStories.length > 0 ? profilesWithStories[0].stories : [];
    }
    else if (followRequestApproved) {
      const profilesWithStories = await getProfilesWithStories({
        user: req.user,
        createdBy: otherUserId,
        visibility: { $in: ['everyone', 'followersAndFriends'] },
      });
      stories = profilesWithStories.length > 0 ? profilesWithStories[0].stories : [];
    }
    else {
      // get stories viewable by everyone
      const profilesWithStories = await getProfilesWithStories({
        user: req.user,
        createdBy: otherUserId,
        visibility: 'everyone',
      });
      stories = profilesWithStories.length > 0 ? profilesWithStories[0].stories : [];
    }

    // save profile view
    await onProfileView(user, otherUser, true);

    // get incoming requests preference
    const allowIncomingRequests = genderPreferenceLib.isIncomingRequestAllowed(otherUser, user);

    let hideFollowButton = false
    const hideFollowFor = new Set(['viewedYou', 'youViewed', 'receivedRequests', 'sentRequests', 'search']);
    const requestFrom = req.query.from || ''
    const hideStoryFor = new Set(['viewedYou', 'youViewed', 'receivedRequests', 'sentRequests', 'search']);
    if (requestFrom && hideStoryFor.has(requestFrom)) {
      stories = []
    }

    return res.json({
      user: {
        hideFollowButton,
        allowIncomingRequests,
        followRequestApproved,
        isMatched,
        isChatExpired,
        dndPost,
        dndMessage,
        stories,
      },
    });
  }));

  router.get('/profile', asyncHandler(async (req, res, next) => {
    const userId = req.query.user;
    const handle = req.query.handle;
    const fromUniverse = req.query.fromUniverse == 'true' || req.query.from == 'universe'
    if (!userId && !handle) {
      return next(invalidInputError());
    }
    if (userId && typeof userId !== 'string') {
      return next(invalidInputError());
    }
    if (handle && typeof handle !== 'string') {
      return next(invalidInputError());
    }

    let user;
    if (userId) {
      user = await User.findById(userId);
    } else if (handle) {
      user = await User.findOne({handle});
    }
    if (!user || (user.shadowBanned && user._id != req.user._id)) {
      return next(notFoundError());
    }

    const blocked = await Block.isEitherBlocked(req.user._id, user._id);
    if (blocked) {
      return next(notFoundError());
    }
    const returnProfileTags = ['viewedYou','youViewed']
    const includeTags = false
    const formattedProfile = chatLib.formatProfile(user, req.user, { isUniverse: fromUniverse ? true : false, includeTags: includeTags});

    return res.json({
      user: formattedProfile,
    });
  }));

  router.patch('/profileView', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const userId = req.query.user;
    const handle = req.query.handle;
    const source = req.query.source;
    if (!userId && !handle) {
      return next(invalidInputError());
    }
    if (userId && typeof userId !== 'string') {
      return next(invalidInputError());
    }
    if (handle && typeof handle !== 'string') {
      return next(invalidInputError());
    }
    if (source && !['for_you'].includes(source)) {
      return next(invalidInputError());
    }

    let otherUser;
    if (userId) {
      otherUser = await User.findById(userId);
    } else if (handle) {
      otherUser = await User.findOne({ handle });
    }
    if (!otherUser || otherUser.shadowBanned) {
      return next(notFoundError());
    }
    if (user._id == otherUser._id) {
      return next(invalidInputError());
    }

    await onProfileView(user, otherUser, true, source);

    return res.json({});
  }));

  router.patch('/profile/notifications', asyncHandler(chatMiddleware.findApprovedChat), asyncHandler(async (req, res, next) => {

    const otherUserId = req.chat.users.filter(({ _id }) => _id != req.uid)[0]._id;
    const updateQuery = { $addToSet: {}, $pull: {} };

    {
      const dndData = [];
      if (req.body.dndPost !== undefined) {
        dndData.push(['dndPostFrom', req.body.dndPost]);
      }
      if (req.body.dndMessage !== undefined) {
        dndData.push(['dndMessageFrom', req.body.dndMessage]);
      }

      if (dndData.length === 0) {
        return next(invalidInputError());
      }

      for (let [dndKey, dndVal] of dndData) {
        if (dndVal === true) {
          updateQuery.$addToSet[dndKey] = otherUserId;
        }
        else if (dndVal === false) {
          updateQuery.$pull[dndKey] = otherUserId;
        }
        else {
          return next(invalidInputError());
        }
      }

    }

    const updated = await Chat.updateOne(
      {
        _id: req.chat._id,
        pendingUser: null,
        groupChat: { $ne: true },
        deletedAt: null
      },
      updateQuery
    );

    if (!updated.acknowledged) {
      return next(applicationError());
    }
    if (updated.matchedCount < 1) {
      return next(notFoundError());
    }

    return res.json({});

  }));

  router.get('/boo', asyncHandler(handleSearch));

  router.put(
    '/initApp',
    asyncHandler(userMiddleware.createUserIfNotExist),
    verifyRecaptcha,
    verifyTurnstile,
    asyncHandler(userMiddleware.findUserMetadata),
    userMiddleware.updateSignInInfo,
    userMiddleware.checkTeleportExpiration,
    asyncHandler(userMiddleware.checkCountryFilterExpiration),
    asyncHandler(async (req, res, next) => {
      const { user } = req;
      const metadata = req.userMetadata;

      const secondSaleNumDays = 7;
      if (
        user.premiumFlashSaleEndDate < new Date()
      && user.metrics.numPurchases == 0
      && !user.premiumExpiration
      && user.metrics.numFlashSales == 1
      && moment().diff(user.createdAt, 'days') >= secondSaleNumDays
      ) {
      // second flash sale
        user.activateFlashSale('second');
      }

      // recurring monthly flash sale
      if (
        moment().diff(user.premiumFlashSaleEndDate, 'days') >= 30
        && user.metrics.numPurchases == 0
        && !user.premiumExpiration
      ) {
        user.events.recurring_monthly_sale += 1;
        user.activateFlashSale('recurring_monthly_sale');
      }

      // recurring weekend sale, once in every weekend
      if (user.createdAt >= new Date('2024-07-30') && !user.premiumExpiration && user.premiumFlashSaleEndDate < new Date()) {
        const createdAtNumDays = moment().diff(user.createdAt, 'days');
        const isWeekendSaleReason = user.premiumFlashSaleReason === 'weekend_sale';
        const flashSaleExpired = moment().diff(moment(user.premiumFlashSaleEndDate), 'hours') > 48;
        const isSaleApplicable = !isWeekendSaleReason || (isWeekendSaleReason && flashSaleExpired);

        if (createdAtNumDays >= 10 && isSaleApplicable) {
          const localTime = DateTime.local().setZone(user.timezone);
          const dayOfWeek = localTime.weekday;
          const hour = localTime.hour;
          // weekend sale can start between Friday 5pm and Sunday 5pm
          if ((dayOfWeek == 5 && hour >= 17) || (dayOfWeek == 6) || (dayOfWeek == 7 && hour < 17)) {
            user.activateFlashSale('weekend_sale', 6);
          }
        }
      }

      // check for god mode super like anniversary
      let receivedFreeSuperLove;
      if (premiumLib.isGodMode(user)) {
        const next = DateTime.fromJSDate(user.godModeMonthlySuperLikeAnniversary).plus({ months: 1 }).toJSDate();
        if (next < new Date()) {
          user.godModeMonthlySuperLikeAnniversary = next;
          user.numSuperLikes += 4;
        }
      }
      if (premiumLib.isPremiumV1(user)) {
        let next = user.infinityWeeklySuperLikeAnniversary;
        if (!next) {
          next = new Date();
        }
        if (next <= new Date()) {
          // skip all missed anniversaries
          while (next <= new Date()) {
            next = DateTime.fromJSDate(next).plus({ weeks: 1 }).toJSDate();
          }
          // note: free super loves do not rollover
          user.infinityWeeklySuperLikeAnniversary = next;
          const previousNumSuperLikesFree = user.numSuperLikesFree || 0;
          user.numSuperLikesFree = 2;
          if (user.numSuperLikesFree > previousNumSuperLikesFree) {
            await SuperLikeTransaction.create({
              user: user._id,
              freeSuperLoveTransactionAmount: user.numSuperLikesFree - previousNumSuperLikesFree,
              freeSuperLoveNewBalance: user.numSuperLikesFree,
              paidSuperLoveTransactionAmount: 0,
              paidSuperLoveNewBalance: user.numSuperLikes,
              description: 'received free super love',
            });
          }
        }
      }
      /*
      else if (user.isConfigTrue('app_334')) {
        let next = user.freeWeeklySuperLikeAnniversary;
        if (!next) {
          next = new Date();
        }
        if (next <= new Date()) {
          // skip all missed anniversaries
          while (next <= new Date()) {
            next = DateTime.fromJSDate(next).plus({ weeks: 1 }).toJSDate();
          }
          // note: free super loves do not rollover
          user.freeWeeklySuperLikeAnniversary = next;
          if (user.numSuperLikesFree == 0) {
            receivedFreeSuperLove = true;
          }
          user.numSuperLikesFree = 1;
        }
      }
      */

      // flash sale for reactivated users
      if (
        user.premiumFlashSaleEndDate < new Date()
        && user.metrics.numPurchases == 0
        && !user.premiumExpiration
        && moment().diff(user.metrics.lastSeen, 'days') >= 7
      ) {
        user.events.flash_sale_after_inactive += 1;
        user.activateFlashSale('flash_sale_after_inactive');
      }

      // check for free weekly super like
      /*
      if (user.isConfigTrue('free_weekly_super_like') && user.numSuperLikes == 0) {
        const next = DateTime.fromJSDate(user.freeSuperLikeReceivedDate).plus({ weeks: 1 }).toJSDate();
        if (!user.freeSuperLikeReceivedDate || next <= new Date()) {
          user.freeSuperLikeReceivedDate = new Date();
          user.numSuperLikes += 1;
        }
      }
      */

      const priorDeviceId = user.deviceId;
      await userLib.processDeviceInfo(req, user);
      const deviceIdChanged = user.deviceId != priorDeviceId;

      if (user.appVersion == '1.12.0') {
        return next(forbiddenError(req.__(userMiddleware.updateAppMessage)));
      }

      userLib.processLocale(req, user);
      user.updateUserLastSeen();
      user.calculateViewableInDailyProfiles();
      user.age = getAge(user.birthday, user.timezone);

      if (user.shadowBanned && user.bannedReason === 'underage birthday' && user.age >= 18) {
        await reportLib.unban(user, null, 'automatic unban - user is now 18 or older');
      }

      // update num followers
      if (!user.metrics.numFollowersUpdatedAt || moment().diff(user.metrics.numFollowersUpdatedAt, 'days') > 0) {
        user.metrics.numFollowersUpdatedAt = new Date();
        if (user.metrics.numFollowers < 1000) {
          const result = await Follow.aggregate([
            {
              $match:
                {
                  to: user._id,
                  approved: true,
                },
            },
            {
              $lookup:
                {
                  from: "users",
                  localField: "from",
                  foreignField: "_id",
                  as: "otherUser",
                },
            },
            {
              $match:
                {
                  "otherUser.0": { $exists: true },
                  "otherUser.shadowBanned": {
                    $ne: true,
                  },
                },
            },
            {
              $count:
                "count",
            },
          ]);
          user.metrics.numFollowers = result[0]?.count || 0;
        }
        if (user.metrics.numFollowing < 1000) {
          const result = await Follow.aggregate([
            {
              $match:
                {
                  from: user._id,
                  approved: true,
                },
            },
            {
              $lookup:
                {
                  from: "users",
                  localField: "to",
                  foreignField: "_id",
                  as: "otherUser",
                },
            },
            {
              $match:
                {
                  "otherUser.0": { $exists: true },
                  "otherUser.shadowBanned": {
                    $ne: true,
                  },
                },
            },
            {
              $count:
                "count",
            },
          ]);
          user.metrics.numFollowing = result[0]?.count || 0;
        }
      }

      if (user.metrics.numPendingLikes < 100) {
        const result = await Chat.aggregate([
          {
            $match: {
              users: user._id,
              pendingUser: user._id,
              deletedAt: null,
              bannedUsers: { $in: [ null, [], user._id ] },
              rejected: { $ne : true },
            },
          },
          {
            $count:
              "count",
          },
        ]);
        user.metrics.numPendingLikes = result[0]?.count || 0;
      }

      // backfill coin rewards
      if (user.audioDescription && !metadata.audioDescriptionRewardReceived) {
        await grantAudioDescriptionReward(user);
      }
      if (user.enneagram && !metadata.enneagramRewardReceived) {
        await grantEnneagramReward(user);
      }
      if (user.personality && user.personality.EI !== null && !metadata.quizAnswerRewardReceived) {
        await grantQuizAnswerReward(user);
      }
      if (user.versionAtLeast('1.11.48')) {
        if (user.pictures.length >= coinsConstants.tierRewards.pictures.tierRequirements[metadata.picturesRewardTier]) {
          const coinReward = await coinsLib.onPicturesUpdated(user);
          socketLib.sendCoinRewards(user._id, coinReward);
        }
        if (user.prompts.length >= coinsConstants.tierRewards.prompts.tierRequirements[metadata.promptsRewardTier]) {
          const coinReward = await coinsLib.onPromptsUpdated(user);
          socketLib.sendCoinRewards(user._id, coinReward);
        }
        if (user.interests.length >= coinsConstants.tierRewards.interests.tierRequirements[metadata.interestsRewardTier]) {
          const coinReward = await coinsLib.onInterestsUpdated(user);
          socketLib.sendCoinRewards(user._id, coinReward);
        }
      }
      if (user.isSocialCoinRewardActive()) {
        if (user.metrics.numQuestions >= coinsConstants.tierRewards.postQuestions.tierRequirements[metadata.postQuestionsRewardTier]) {
          const coinReward = await coinsLib.onPostQuestions(user);
          socketLib.sendCoinRewards(user._id, coinReward);
        }
        if (user.metrics.numComments >= coinsConstants.tierRewards.postComments.tierRequirements[metadata.postCommentsRewardTier]) {
          const coinReward = await coinsLib.onPostComments(user);
          socketLib.sendCoinRewards(user._id, coinReward);
        }
        if (user.metrics.numPostLikesSent >= coinsConstants.tierRewards.giveLikes.tierRequirements[metadata.giveLikesRewardTier]) {
          const coinReward = await coinsLib.onGiveLikes(user);
          socketLib.sendCoinRewards(user._id, coinReward);
        }
        if (user.metrics.numPostLikesReceived >= coinsConstants.tierRewards.getLikes.tierRequirements[metadata.getLikesRewardTier]) {
          const coinReward = await coinsLib.onGetLikes(user);
          socketLib.sendCoinRewards(user._id, coinReward);
        }
        if (user.metrics.numAwardsSent >= coinsConstants.tierRewards.giveAwards.tierRequirements[metadata.giveAwardsRewardTier]) {
          const coinReward = await coinsLib.onGiveAwards(user);
          socketLib.sendCoinRewards(user._id, coinReward);
        }
        if (user.metrics.numAwardsReceived >= coinsConstants.tierRewards.getAwards.tierRequirements[metadata.getAwardsRewardTier]) {
          const coinReward = await coinsLib.onGetAwards(user);
          socketLib.sendCoinRewards(user._id, coinReward);
        }
      }

      // check for global only
      if (countryLib.group1Names.includes(user.signupCountry) && !premiumLib.isPremium(user)) {
        if (user.preferences.global == true && user.preferences.local == false) {
          user.preferences.local = true;
        }
      }

      // convert auto temp shadow ban to shadow hide
      if (user.profileTempBanReason && user.bannedBy == null) {
        await reportLib.undoProfileTempBan(user);

        if (!user.originalFields.description) {
          user.originalFields.description = user.description;
          user.description = removeBannedKeywords(user.description);
        }

        if (!user.originalFields.education) {
          user.originalFields.education = user.education;
          user.education = removeBannedKeywords(user.education);
        }

        if (!user.originalFields.work) {
          user.originalFields.work = user.work;
          user.work = removeBannedKeywords(user.work);
        }

        if (!user.originalFields.prompts) {
          user.originalFields.prompts = user.prompts;
          user.prompts = user.prompts.map((p) => ({
            id: p.id,
            prompt: p.prompt,
            answer: removeBannedKeywords(p.answer),
          }));;
        }
      }

      if (!user.termsAcceptedDate) {
        user.termsAcceptedDate = user.createdAt;
        user.termsAcceptedHistory.push(user.createdAt);
      }

      await user.resetCurrentDayMetricsIfNeeded();

      if (user.isConfigTrue('app_165') && user.currentDayMetrics.showSecretAdmirerOnSwipe == null) {
        const daysOnPlatform = user.getDaysOnPlatform();
        let eligible = false;
        if (user.metrics.receivedSecretAdmirerOnDay.length == 0) {
          eligible = true;
        } else {
          const mostRecentReceived = user.metrics.receivedSecretAdmirerOnDay[user.metrics.receivedSecretAdmirerOnDay.length - 1];
          if (daysOnPlatform >= 4 && daysOnPlatform <= 7 && mostRecentReceived < 4) {
            eligible = true;
          }
          if (daysOnPlatform >= 8 && daysOnPlatform <= 30 && mostRecentReceived < 8) {
            eligible = true;
          }
        }
        if (eligible) {
          const chats = await chatLib.getPendingChats(user);
          const numBlurredChats = chats.filter(x => !x.initiatedBySuperLike && !x.lastMessage).length;
          if (numBlurredChats >= 4) {
            user.currentDayMetrics.showSecretAdmirerOnSwipe = _.sample([3, 4, 5]);
          } else {
            user.currentDayMetrics.showSecretAdmirerOnSwipe = -1;
          }
        } else {
          user.currentDayMetrics.showSecretAdmirerOnSwipe = -1;
        }
      }

      if (user.email && !user.emailDomain) {
        user.emailDomain = basic.extractEmailDomain(user.email);
      }

      if (!user.appsflyer?.appsflyer_id && req.body?.appsflyer_id){
        user.appsflyer.appsflyer_id = req.body.appsflyer_id
      }

      if (user.versionAtLeast('1.13.85') && user.ipData?.country == 'Brazil') {
        user.accountDeletionGracePeriod = 15;
      }

      await user.save();

      if (deviceIdChanged) {
        await actionLib.blockSameDeviceUsers(user, res.io);
        HideList.userHiddenBy(user._id, null, null, user.deviceId).then(
          (data) => Promise.all(data.map((blockById) => blockUser(blockById, user._id, res.io, false, 'DeviceId', undefined, user.deviceId))),
          (err) => { `User ${req.uid}: Error updating hidden users based on DeviceId after update Device Info: `, err; },
        );
      }

      await updateUserScore(user);

      await checkTimezoneCountry(user);

      let numPendingChats = 0;
      if (user.appVersion && cmp(user.appVersion, '1.10.19') == 0) {
        const query = {
          users: req.uid,
          pendingUser: req.uid,
        };
        numPendingChats = await Chat.countDocuments(query);

        // get approximate count of filtered chats
        const unfilteredChats = await Chat
          .find(query)
          .sort('-lastMessageTime')
          .limit(pageSize)
          .populate('users')
          .lean();
        const filteredChats = chatLib.filterChats(unfilteredChats, req.user);
        const numBannedChats = unfilteredChats.length - filteredChats.length;
        numPendingChats -= numBannedChats;
      }

      let newTermsAcceptanceRequired;
      const termsDate = constants.getTermsDate();
      if (termsDate) {
        if (!user.termsAcceptedDate || user.termsAcceptedDate < termsDate) {
          newTermsAcceptanceRequired = true;
        }
      }

      if ((user.metrics?.revenue > 0 && user.versionAtLeast('1.13.88')) || countryLib.gdprCountries.includes(user.ipData?.country)) {
        await reportLib.evaluateBanNotice(user);
      }

      if (countryLib.gdprCountries.includes(user.ipData?.country)) {
        if (!user.accountRestrictions) {
          user.accountRestrictions = {};
        }
        if (user.profileTempBanReason && !user.accountRestrictions.profileTempBan) {
          user.accountRestrictions.profileTempBan = { appealStatus: 'allowed' };
          await user.save()
        }
      }

      const formattedProfile = await userLib.formatMyProfile(user, req.userMetadata, req.ip);
      const rv = {
        user: formattedProfile,
        showDatingSubPreferencesPopup: user.hasOwnProperty('datingSubPreferences') ? false : (user.datingPreferencesPopupClosed ? false : true),
        showRelationshipTypePopup: user.preferences?.dating?.length ? (user.hasOwnProperty('relationshipType') ? false : (user.relationshipTypePopupClosed ? false : true)) : false,
        showSexualityPopup: userLib.showSexualityPopup(user),
        coinProducts: coinsLib.getCoinProducts(user),
        interests: interestLib.getAllInterests(user),
        numPendingChats,
        karmaTiers: getKarmaTiers(),
        karmaTierCoinRewards: getKarmaTiers(),
        karmaTierSwipeLimits: constants.getKarmaTierSwipeLimits(),
        karmaTierGhostMeter: constants.getKarmaTierGhostMeter(),
        newTermsAcceptanceRequired,
        receivedFreeSuperLove,
        numYourTurnChats : user.metrics.numYourTurnChats || 0,
      };

      rv.nextLoginReward = basic.getPreviousRewardResetTime(user);
      if (user.metrics.loginRewardReceivedTime > rv.nextLoginReward) {
        rv.nextLoginReward = basic.getCurrentDayResetTime(user.timezone);
      }

      if (!metadata.rateAppRewardReceived && user.promptAppRating()) {
        rv.promptAppRating = true;
      }

      if (!user.versionAtLeast('1.11.42')) {
        rv.avatars = personalityLib.getAllAvatars(user.locale);
        rv.telepathyDescriptions = telepathyLib.getAllTelepathyDescriptions(user);
        rv.threeTierPersonalityRecommendations = personalityLib.getThreeTierPersonalityRecommendations(user);
      }

      if (user.versionAtLeast('1.13.49')) {
        rv.freeSwipeFromQodReceived = user.currentDayMetrics.freeSwipeFromQodReceived;
        rv.freeSwipeReceivedForQods = user.metrics.freeSwipeReceivedForQods;
      }

      if (user.versionAtLeast('1.11.74')) {
        if (user.lastSentKarma === undefined) {
          rv.karmaChange = 0;
        }
        else {
          rv.karmaChange = user.karma - user.lastSentKarma;
        }
        if (user.lastSentKarma != user.karma) {
          user.lastSentKarma = user.karma;
          await user.save();
        }
      }
      if (user.versionAtLeast('1.13.51')) {
        const numLikesReceivedChange = user.metrics.numPendingLikes - (user.lastSentNumPendingLikes || 0);
        if (numLikesReceivedChange > 0) {
          rv.numLikesReceivedChange = numLikesReceivedChange;
        }
        if (user.lastSentNumPendingLikes != user.metrics.numPendingLikes) {
          user.lastSentNumPendingLikes = user.metrics.numPendingLikes;
          await user.save();
        }
      }

      const numLikesReceivedDuringBoost = await getNumLikesReceivedDuringBoost(user)
      if(numLikesReceivedDuringBoost) rv.numLikesReceivedDuringBoost = numLikesReceivedDuringBoost

      if (req.newSignup) {
        //conversionEmitter.emit(constants.conversionEvent.SIGNUP, req);
        rv.newSignup = true;
      }
      rv.purchaseInfo = await getPurchaseInfo(user);

      /**
       * This code calls `ExperimentRecording.createAndSave` under the following conditions:
       * 1. When a new user signs up before completing onboarding (rv.newSignup == true).
       * 2. When a new user signs up and completes onboarding.
       *
       * The `user.recordingAllocationEvaluated` flag indicates whether the user's recording allocation has been evaluated:
       * - For users who signed up before this code went live in production, `user.recordingAllocationEvaluated` will be undefined, and will not recorded.
       * - For users who sign up after this code goes live, `user.recordingAllocationEvaluated` will initially be set to `false`.
       *
       * After the user is recorded, `user.recordingAllocationEvaluated` is set to `true` to prevent future calls to `ExperimentRecording.createAndSave`
       * when the user opens the app again. Since the user has already been recorded, any subsequent calls would return a `record = false`, making them unnecessary.
       */
      if(rv.newSignup){
        const record = await ExperimentRecording.createAndSave(user);
        rv.recordingTrigger = record.recordingTrigger;
        user.recordingAllocationEvaluated = false
        await user.save()

      }else if(user.recordingAllocationEvaluated === false && user.os && user.deviceSize && user.age && user.gender && (user.preferences.dating.length > 0 || user.preferences.friends.length > 0)){
        const record = await ExperimentRecording.createAndSave(user);
        rv.recordingTrigger = record.recordingTrigger;
        if(rv.recordingTrigger.record === true){
          user.recordingAllocationEvaluated = true
        }
        await user.save()

      }

      // check if support account
      if (user._id === chatLib.BOO_SUPPORT_ID) {
        rv.is_boo_support = true;
      }

      // rate translation logic
      const translationConfigLanguage = getTranslationConfigLanguage(user);
      if (translationConfigLanguage && user.translationRatings[translationConfigLanguage] === undefined) {
        rv.promptTranslationRating = translationConfigLanguage;
      }

      res.json(rv);

      if (user.exclusionListFailed || user.metrics.numActionsSent > 400000) {
       if(user.exclusionListFailed || user.metrics.currentExclusionListSize > 400000 || (user.metrics.numActionsSent > 400000 && !(user.metrics.currentExclusionListSize >= 0))){
         await actionLib.recalculateExclusionList(user._id, true)
        }
      } else if (user.lastExclusionRecalculated) {
        await actionLib.recalculateExclusionList(user._id, true)
        user.lastExclusionRecalculated = undefined
        await user.save()
      }

      if (user.versionAtLeast('1.13.69')) {
        // Day 1: Send tips if user needs match advice
        if (!user.booMessages?.dayOneTrigger) {
          const { numLikesReceived, numMatches } = user.metrics;
          const needsMatchTips = numLikesReceived === 0 || (numLikesReceived > 0 && numMatches === 0 && !isPremium(user));

          await sendConditionalMessage(
            user,
            moment().diff(user.createdAt, 'days') === 1 && needsMatchTips,
            'dayOneTrigger',
            true,
            'If you’re having difficulties getting matches, try these helpful tips:\n\n1. Upload clear photos with your face.\n2. Verify your profile to build trust and show you’re real.\n3. Complete your profile to 100% with your interests and hobbies so others can learn more about you.\n4. Stay active daily and send loves. Matches happen when two souls send love to each other.',
          );
        }

        // Trigger for enable notifications
        if (user.booMessages?.notificationTriggerInit) {
          const minutesSinceInit = moment().diff(user.booMessages.notificationTriggerInit, 'minutes');
          if (minutesSinceInit > 5) {
            if (!user.fcmToken) {
              const buttonText = translate('Enable notifications', user.locale || 'en');
              const messageText = translate('{{messageSender}} sent you a message. Enable notifications to stay updated and not miss out on any matches or messages.', user.locale || 'en', { messageSender: user.booMessages.messageSender });

              // Message structure for versions prior to 1.13.73
              let replyText = `${messageText}\n\n${buttonText}`;
              let additionalInfo = { replaceText: buttonText, openPage: 'notificationPage' };

              // reply text will have markdown for the button
              if (user.versionAtLeast('1.13.73')) {
                replyText = `${messageText}\n\n[${buttonText}](/notificationPage)`;
                additionalInfo = undefined;
              }

              await chatLib.sendAutomatedReply(user, replyText, true, additionalInfo);
            }
            user.booMessages.notificationTrigger = true;
            user.booMessages.notificationTriggerInit = undefined;
            user.booMessages.messageSender = undefined;
            await user.save();
          }
        }

        // Day 3: Encourage use of filters
        const isPopupNotOpened = !user.events?.open_filter_popup;
        const isDayThreeNotTriggered = !user.booMessages?.dayThreeTrigger;

        if (isPopupNotOpened && isDayThreeNotTriggered) {
          await sendConditionalMessage(
            user,
            moment().diff(user.createdAt, 'days') === 3,
            'dayThreeTrigger',
            true,
            `Personalize your experience with flexible filters. Discover your soulmate based on interests, personality, and lifestyle.`,
            {},
            { replaceText: 'Change Filter', openPage: 'filter' },
          );
        }

        // Prepare sale events trigger
        const saleDetails = [
          {
            saleEndDate: user.premiumFlashSaleEndDate,
            triggerDateKey: 'infinitySaleTriggerDate',
            messageTemplate: 'Limited time: Save 50% on Boo Infinity for the next {{placeholder}}.\n\nUpgrade and unlock unlimited Loves and DMs, See Who Loves You, 2 Super Loves per week and more.',
            templateParams: {},
            additionalInfo: { replaceText: 'Upgrade', openPage: 'infinitySalePage' },
          },
          /* Disabled for later experiment
          {
            saleEndDate: user.superLikeFlashSaleEndDate,
            triggerDateKey: 'superLikeSaleTriggerDate',
            messageTemplate: 'Limited time: Save 50% on Super Loves for the next {{placeholder}}.\n\nSuper Loves are pinned at the top of requests and can lead to up to {{matchPlaceholder}}% more matches.',
            templateParams: { matchPlaceholder: '198' },
            additionalInfo: { replaceText: 'Get Super Loves', openPage: 'superLikeSalePage' },
          },
          */
          {
            saleEndDate: user.coinsFlashSaleEndDate,
            triggerDateKey: 'coinsSaleTriggerDate',
            messageTemplate: 'Limited time: Save 30% on coins for the next {{placeholder}}.\n\nWith your coins, unlock features like DMs, Time Travel, and Revival.',
            templateParams: {},
            additionalInfo: { replaceText: 'Get Coins', openPage: 'coinsSalePage' },
          },
          {
            saleEndDate: user.boostsFlashSaleEndDate,
            triggerDateKey: 'boostsSaleTriggerDate',
            messageTemplate: 'Limited time: Save 50% on Boost for the next {{placeholder}}.\n\nActivate now to become a top soul in your area for the next 60 minutes.',
            templateParams: {},
            additionalInfo: { replaceText: 'Activate', openPage: 'boostsSalePage' },
          },
        ];

        // Loop through and send sale messages
        for (const { saleEndDate, triggerDateKey, messageTemplate, templateParams, additionalInfo } of saleDetails) {
          // check if there is any active sale and not first flash sale
          if (triggerDateKey === 'infinitySaleTriggerDate' && user.premiumFlashSaleReason === 'first') {
            // eslint-disable-next-line no-continue
            continue;
          }

          const remainingTime = userLib.getRemainingSaleTime(saleEndDate, user.locale || 'en');
          if (remainingTime) {
            const lastTriggered = user.booMessages[triggerDateKey];
            await sendConditionalMessage(
              user,
              !lastTriggered || (lastTriggered && moment().diff(moment(lastTriggered), 'hours') > 6),
              triggerDateKey,
              Date.now(),
              messageTemplate,
              { ...templateParams, placeholder: remainingTime },
              additionalInfo,
            );
          }
        }
      }

      // Trigger for whats new bot message
      if (user.versionAtLeast('1.13.70')) {
        await handleWhatsNewMessage(user);
      }

      // backfill userPerState in chats
      if (user.createdAt < constants.getBackfillChatPerUserStateCutoffDate() && !user.backfillChatPerUserStateComplete && (!user.backfillChatPerUserStateBegan || user.backfillChatPerUserStateBegan < new Date(Date.now() - 24 * 60 * 60 * 1000))) {
        user.backfillChatPerUserStateBegan = new Date()
        await chatLib.backfillUserPerStateInChats(user._id)
        user.backfillChatPerUserStateComplete = true
        await user.save()
      }

      // backfill numYourTurnChats in user
      if ((user.createdAt > constants.getBackfillChatPerUserStateCutoffDate() || user.backfillChatPerUserStateComplete) && user.createdAt < constants.getBackfillNumYourTurnChatsCutoffDate() && !user.backfillNumYourTurnChatsComplete && (!user.backfillNumYourTurnChatsBegan || user.backfillNumYourTurnChatsBegan < new Date(Date.now() - 24 * 60 * 60 * 1000))) {
        user.backfillNumYourTurnChatsBegan = new Date()
        user.metrics.numYourTurnChats = await chatLib.backfillUserNumYourTurnChats(user._id)
        user.backfillNumYourTurnChatsComplete = true
        await user.save()
      }

      if(user.metrics?.numYourTurnChats > 8){
        await chatLib.finalizeYourTurnStateInChats(user._id)
      }

    }),
  );

  router.get('/boostPopup', asyncHandler(async (req, res, next) => {
    const user = req.user;
    const numLikesReceivedDuringBoost = await getNumLikesReceivedDuringBoost(user)
    numLikesReceivedDuringBoost ? res.json({'numLikesReceivedDuringBoost' : numLikesReceivedDuringBoost}) : res.json({})
  }));

  router.put('/translationRating', asyncHandler(async (req, res, next) => {
    const user = req.user;
    const { language, rating } = req.body;

    if (rating !== null && ![1,2,3,4,5].includes(rating)) {
      return next(invalidInputError());
    }

    const translationConfigLanguage = getTranslationConfigLanguage(user);
    if (!translationConfigLanguage || language !== translationConfigLanguage || user.translationRatings[translationConfigLanguage] !== undefined) {
      return next(invalidInputError());
    }

    user.translationRatings[translationConfigLanguage] = rating;
    await user.save();

    await TranslationRating.create({
      user: user._id,
      language,
      rating,
    });

    res.json({});
  }));

  router.put('/acceptTerms', asyncHandler(async (req, res, next) => {
    const user = req.user;
    const date = new Date();
    user.termsAcceptedDate = date;
    user.termsAcceptedHistory.push(date);
    await user.save();
    res.json({});
  }));

  router.put('/acceptVerificationTerms', asyncHandler(async (req, res, next) => {
    const user = req.user;
    const date = new Date();
    user.verificationTermsAcceptedDate = date;
    await user.save();
    res.json({});
  }));

  router.put('/logout', asyncHandler(async (req, res, next) => {
    const user = req.user;
    const date = new Date();
    user.metrics.loggedOutAt = date;
    user.fcmToken = null;
    user.fcmTokenUpdatedAt = date;
    await user.save();
    res.json({});
  }));

  router.put('/locale', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    userLib.processLocale(req, user);
    await user.save()
    res.json({});
  }));

  router.put('/deviceInfo', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;

    const priorDeviceId = user.deviceId;
    await userLib.processDeviceInfo(req, user);
    const deviceIdChanged = user.deviceId != priorDeviceId;

    await user.save();

    if (deviceIdChanged) {
      await actionLib.blockSameDeviceUsers(user, res.io);
      HideList.userHiddenBy(user._id, null, null, user.deviceId).then(
        (data) => Promise.all(data.map((blockById) => blockUser(blockById, user._id, res.io, false, 'DeviceId', undefined, user.deviceId))),
        (err) => { `User ${req.uid}: Error updating hidden users based on DeviceId after update Device Info: `, err; },
      );
    }
    res.json({});
  }));

  router.put('/openApp', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    if (req.body.appVersion) { user.appVersion = req.body.appVersion; }
    if (req.body.os) { user.os = req.body.os; }
    if (req.body.osVersion) { user.osVersion = req.body.osVersion; }
    if (req.body.phoneModel) { user.phoneModel = req.body.phoneModel; }
    if (req.body.timezone && momentTz.tz.zone(req.body.timezone) != null) {
      user.timezone = req.body.timezone;
    }
    const coinReward = await coinsLib.onLogin(user);
    const { coins, rewards } = await coinsLib.getCoins(user);
    const savedUser = await user.save();
    await notificationLib.resetNotificationBadgeCount(req.uid);
    const rv = {
      coins,
      coinReward: coinReward || undefined,
    };
    if (user.promptAppRating()) {
      rv.promptAppRating = true;
    }
    return res.json(rv);
  }));

  router.put('/closeApp', asyncHandler(async (req, res, next) => {
    await notificationLib.resetNotificationBadgeCount(req.uid);
    res.json({});
  }));

  router.put('/usePromoCode', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    const promoCode = await PromoCode.findOne({ promoCode: req.body.promoCode.toLowerCase() });
    if (!promoCode) {
      return res.json({
        valid: false,
      });
    }

    user.savedPromoCode = promoCode.promoCode;
    await user.save();
    return res.json({
      valid: true,
      discount: promoCode.discount,
    });
  }));

  router.put('/purchasePremium', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    const errorMsg = 'Purchase could not be validated.';

    if (req.body.productIdPurchased) {
      user.productIdPurchased = req.body.productIdPurchased;
      await user.save();
    }

    await user.updateNumMinutesUntilParams('numMinutesUntilFirstPurchase');

    if (!req.body.receipt) {
      return next(invalidInputError(errorMsg));
    }

    iap.validate(req.body.receipt, async (error, validatedData) => {
      if (error) {
        console.log(error, validatedData);
        if (
          validatedData && validatedData.status == 2 // hacked apple receipt
          || error.message == 'Status:400' // hacked google receipt
          || error.message == 'Status:403' // google receipt from elsewhere
        ) {
          console.log('Potentially a hacked receipt');
          return next(invalidInputError(errorMsg));
        }
        return next(applicationError(errorMsg));
      }

      const options = {
        // Must set ignoreCanceled to avoid rejecting valid purchases
        // See: https://github.com/voltrue2/in-app-purchase/issues/342
        ignoreCanceled: true, // Apple ONLY
      };
      const purchaseData = iap.getPurchaseData(validatedData, options);

      console.log('validatedData', JSON.stringify(validatedData, null, 2));
      console.log('purchaseData', JSON.stringify(purchaseData, null, 2));

      const receipt = await processPurchaseData(user, purchaseData, req.body.purchasedFrom, req.body.price, req.body.currency)
      if (user.os == 'ios') {
        let purchaseData = null;
        if (receipt && receipt.revenue > 0) {
          purchaseData = {
            productId: receipt.productId,
            price: receipt.price,
            currency: receipt.currency,
          };
        }
        await PurchasePremiumResponse.create({
          user: user._id,
          productId: purchaseData?.productId,
          currency: purchaseData?.currency,
          price: purchaseData?.price,
          revenue: purchaseData ? receipt?.revenue : undefined,
        });
        return res.json({
          purchaseData,
        });
      }
      res.json({});
    });
  }));

  router.delete('/', asyncHandler(async (req, res, next) => {
    const { user } = req;

    if (user._id == constants.IMMEDIATE_DELETION_ID || user.phoneNumber == '+***********') {
      console.log(`Immediate deletion for user ${user._id}`);
      await userLib.deleteAccount(user);
      console.log(`Removed user ${user._id}`);
      return res.json({});
    }

    if (user.deletionRequestDate) {
      return res.json({});
    }

    let reason = [];
    if (req.query.reason) {
      try {
        // req.query.reason has format '1,3,4'
        reason = req.query.reason.split(',').map(Number);
        if (reason.some(isNaN)) {
          return next(invalidInputError());
        }
      } catch (err) {
        return next(invalidInputError());
      }
    }
    user.deletionRequestDate = Date.now();
    const savedUser = await user.save()
    const attempt = new DeleteAccountAttempt({
      createdAt: savedUser.deletionRequestDate,
      user: savedUser._id,
      status: 'pending',
      reason,
    });
    await attempt.save();

    return res.json({});
  }));

  router.put('/additionalSwipes', asyncHandler(async (req, res, next) => {
    req.user.metrics.numAdditionalSwipes += 5;
    req.user.currentDayMetrics.numAdditionalSwipes += 5;
    await req.user.save();
    res.json({});
  }));

  router.post('/accountDeletion', asyncHandler(async (req, res, next) => {
    const { user } = req;

    if (user.deletionRequestDate) {
      return res.json({});
    }

    const { reason } = req.body;
    if (!Array.isArray(reason) || reason.some(isNaN)) {
      return next(invalidInputError());
    }

    const { feedback } = req.body;
    if (feedback && (typeof feedback !== 'string' || feedback.length > 10000)) {
      return next(invalidInputError());
    }

    const deletionRequestDate = Date.now();

    const attempt = new DeleteAccountAttempt({
      createdAt: deletionRequestDate,
      user: user._id,
      status: 'pending',
      reason,
      feedback,
    });
    await attempt.save();

    if (user._id == constants.IMMEDIATE_DELETION_ID || user.phoneNumber == '+***********') {
      console.log(`Immediate deletion for user ${user._id}`);
      await userLib.deleteAccount(user);
      console.log(`Removed user ${user._id}`);
      return res.json({});
    }

    user.deletionRequestDate = deletionRequestDate;
    user.metrics.numDeleteAccountAttempts += 1;
    user.fcmToken = undefined;
    user.hidden = true;
    user.calculateViewableInDailyProfiles();
    await user.save();

    return res.json({});
  }));

  router.patch('/cancelAccountDeletion', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    if (!user.deletionRequestDate) {
      return res.json({});
    }
    const { deletionRequestDate } = user;
    user.deletionRequestDate = undefined;
    user.metrics.numDeleteAccountAttemptsCancelled += 1;
    user.hidden = false;
    user.calculateViewableInDailyProfiles();
    const savedUser = await user.save();
    const attempt = await DeleteAccountAttempt.findOne({
      user: savedUser._id,
      createdAt: deletionRequestDate,
    });
    if (!attempt) {
      return res.json({});
    }
    attempt.status = 'canceled';
    await attempt.save();
    return res.json({});
  }));

  router.put('/interests', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    let interestIds;
    const oldInterests = user.interestNames;
    let newInterests;
    let validateInterestData = []
    if (req.body.interestNames) {
      const interestNames = interestLib.cleanInterestNames(req.body.interestNames);
      const interests = await interestLib.validateInterestNames(interestNames);
      if (!interests) {
        return next(invalidInputError());
      }
      validateInterestData = interests;
      interestIds = interests.map((x) => x._id);
      newInterests = interestNames;
    } else if (req.body.interestIds) {
      const interestNames = interestLib.getInterestNamesFromIds(req.body.interestIds);
      if (!interestNames) {
        return next(notFoundError());
      }
      validateInterestData = await interestLib.validateInterestNames(interestNames);
      if (!validateInterestData) {
        return next(invalidInputError());
      }
      interestIds = req.body.interestIds;
      newInterests = interestNames;
    } else {
      return next(badRequestError());
    }

    user.interests = interestIds;
    user.interestNames = newInterests;
    const added = arrayDiff(newInterests, oldInterests);
    const removed = arrayDiff(oldInterests, newInterests);
    if (!user.events['finished_signup']) {
      const returnEvents = getOnboardingInterestsEvents(user.interestNames, user.locale)
      const keys = Object.keys(returnEvents);
      for (const key of keys) {
        user.events[key] = 1;
      }

      // the experiment for onboarding_video_gaming_women has concluded, but keeping this code here in a comment as reference for how to implement similar experiments later
      /*
      // partnerCampaign takes precedence over interests
      if (user.partnerCampaign == 'gaming' && user.locale == 'en' && user.gender == 'female' && user.config.onboarding_video_gaming_women == undefined && user.versionAtLeast('1.13.79')) {
        user.config.onboarding_video_gaming_women = basic.assignConfig(0.5);
      }
      else if (user.events.added_interest_gaming && user.locale == 'en' && user.gender == 'female' && user.config.onboarding_video_gaming_women == undefined && user.versionAtLeast('1.13.79')) {
        user.config.onboarding_video_gaming_women = basic.assignConfig(0.5);
      }
      */
    }
    await user.save();
    /*
    Maybe following can be made async
     */
    // update interest metrics
    if (added.length || removed.length) {
      const bulk = Interest.collection.initializeUnorderedBulkOp();
      for (const name of added) {
        const interest = validateInterestData.find(interest => interest.name === name);
        if (interest) {
          await InterestCountryCount.incrementCount(interest.name, interest.libCategory || interest.category || null, user.ipData?.country, user.locale);
        }
        const filter = { name };
        const updates = { $inc: { numFollowers: 1, numFollowersSortIndex: 1 } };
        bulk.find(filter).update(updates);
      }
      for (const name of removed) {
        await InterestCountryCount.decrementCount(name, user.ipData?.country, user.locale);
        const filter = { name };
        const updates = { $inc: { numFollowers: -1, numFollowersSortIndex: -1 } };
        bulk.find(filter).update(updates);
      }
      await bulk.execute();
    }

    const rewards = await onInterestsUpdated(user);
    socketLib.sendCoinRewards(user._id, rewards);

    if (user.interestNames.includes('instagram') && user.interestNames.includes('ciara')) {
      await reportLib.shadowBan(user, null, 'spam in interests', 'ciara');
    }

    res.json({});
  }));

  router.put('/hiddenInterests', asyncHandler(async (req, res, next) => {
    const { user } = req;

    const interestNames = interestLib.cleanInterestNames(req.body.interestNames);
    const interests = await interestLib.validateInterestNames(interestNames);
    if (!interests) {
      return next(invalidInputError());
    }

    user.hiddenInterests = interestNames;
    await user.save();

    res.json({});
  }));

  router.put('/kochava', asyncHandler(async (req, res, next) => {
    const { user } = req;
    user.kochava = req.body.kochava;
    await user.save();

    for (const model of [PurchaseReceipt, CoinPurchaseReceipt, SuperLikePurchaseReceipt, NeuronPurchaseReceipt, StripeReceipt]) {
      await model.updateMany(
        { user: user._id },
        {
          kochava: user.kochava,
          kochavaNetwork: user.kochava.network,
        }
      );
    }

    res.json({});
  }));

  router.put('/appsflyer', asyncHandler(async (req, res, next) => {
    try {
      const { user } = req;

      if(req.body.appsflyer.status){
        user.appsflyer.status = req.body.appsflyer.status
      }

      if(req.body.appsflyer.payload){
        user.appsflyer.payload = req.body.appsflyer.payload
      }

      if(req.body.appsflyer.appsflyer_id && !user.appsflyer.appsflyer_id){
        user.appsflyer.appsflyer_id = req.body.appsflyer.appsflyer_id
      }

      const result = await appsflyerToKochava(user.appsflyer)
      if (result) {
        user.kochava = result;
      }

      if (!user.partnerCampaign) {
        const niche = getNicheFromCampaign(user);
        if (niche) {
          user.partnerCampaign = niche;
        }
      }

      await user.save();

      res.json({
        partnerCampaign: user.partnerCampaign,
      });
    } catch (error) {
      console.log('Failed to set appsflyer data, error: ', error)
      return next(badRequestError());
    }

  }));

  router.patch('/events', asyncHandler(async (req, res, next) => {
    const { user } = req;

    if (req.body.exitFlashSale) {
      if (!user.events.exitFlashSale) {
        if (user.verification.status == 'rejected' && user.versionAtLeast('1.13.30')) {
          userLib.notifyVerificationRejection(user);
        }
      }
      if (user.premiumFlashSaleOneTime) {
        user.premiumFlashSaleOneTime = undefined;
        user.premiumFlashSaleEndDate = Date.now();
      }
      if (user.versionAtLeast('1.13.80') && !user.metrics.numFlashSales) {
        user.activateFlashSale('first', 6);
      }
    }

    // Check if user has finished signing up and meets version requirements
    if (req.body.finished_signup && user.versionAtLeast('1.13.69') && !user.booMessages?.welcomeMessage) {
      let messageTemplate = '';
      let templateParams, additionalInfo;

      // Define the message template based on user status
      if (user.pictures.length > 0) {
        if (user.verification.status !== 'verified') {
          messageTemplate = 'Welcome to Boo! Verify your profile to start messaging, build trust, get shown more often, and receive up to {{matchPlaceholder}}% more matches.';
          templateParams = { matchPlaceholder: '198' };
          additionalInfo = { replaceText: 'Verify', openPage: 'verifyPage' };
        } else if (userLib.calculateProfileCompletion(user) >= 1) {
          messageTemplate = 'Welcome to Boo! Connect with souls who share your interests and find helpful tips here along the way.';
        } else {
          messageTemplate = 'Welcome to Boo! You’re almost there. Completing your profile to 100% helps other souls get to know you better and can lead to up to {{matchPlaceholder}}% more matches.';
          templateParams = { matchPlaceholder: '198' };
        }
      }

      const condition = messageTemplate !== '';

      // Send the welcome message and mark it sent
      await sendConditionalMessage(
        user,
        condition,
        'welcomeMessage',
        true,
        messageTemplate,
        templateParams,
        additionalInfo,
      );
    }

    if (req.body.finished_signup) {
      await user.updateNumMinutesUntilParams('numMinutesUntilFinishSignup');
    }

    user.updateEvents(req.body);

    if (req.body.load_yoti && user.verification.numSecondsToCompleteYoti === undefined) {
      user.verification.loadedYotiAt = Date.now();
    }

    await user.save();

    res.json({});
  }));

  router.patch('/claimLoginReward', asyncHandler(async (req, res, next) => {
    const { user } = req;

    if (user.metrics.loginRewardReceivedTime > basic.getPreviousRewardResetTime(user)) {
      return res.json({});
    }

    user.metrics.loginRewardReceivedTime = new Date();
    await user.save();

    await socialLib.incrementKarma(user._id, 1);

    res.json({});
  }));

  // to set router for all options
  for (const key in moreAboutUserChoices) {
    router.put(`/moreAboutUser/${key}`, asyncHandler(async (req, res, next) => {
      const value = req.body[key];
      if (value && !moreAboutUserChoices[key].includes(value)) {
        throw invalidInputError();
      }

      req.user.moreAboutUser[key] = value;
      await req.user.save();
      res.json({});
    }));

    router.put(`/preferences/${key}`, asyncHandler(async (req, res, next) => {
      const { values } = req.body;

      const choices = moreAboutUserChoices[key];
      if (
        !Array.isArray(values)
        || values.length > choices.length
        || hasDuplicates(values)
        || values.some((x) => !choices.includes(x))) {
        throw invalidInputError();
      }

      req.user.preferences[key] = values;
      await req.user.save();

      res.json({});
    }));
  }

  router.post('/submitTranslation', asyncHandler(async (req, res, next) => {
    const { currentTranslation } = req.body;
    const { correctTranslation } = req.body;
    const { details } = req.body;
    const { language } = req.body;
    if (
      (typeof currentTranslation !== 'string' || currentTranslation.length > 500)
      || (typeof correctTranslation !== 'string' || correctTranslation.length > 500)
      || (details !== undefined && (typeof details !== 'string' || details.length > 2000))
      || (typeof language !== 'string' || !languageCodes.includes(language))
    ) {
      throw invalidInputError();
    }
    const data = await postTranslation(req.user, currentTranslation, correctTranslation, language, details);
    if (!data) {
      throw applicationError();
    }
    return res.json(data);
  }));

  router.post('/translation/image', validUserTranslation, s3.uploadTranslationImage.single('image'), asyncHandler(async (req, res, next) => {
    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError('Image was not provided'));
    }
    req.translation.refImage = req.file.key;
    await req.translation.save();
    return res.json({});
  }));

  router.get('/questions', asyncHandler(getUserQuestions));
  router.get('/comments', asyncHandler(getUserComments));

  router.get('/anonymousQuestions', asyncHandler(getUserAnonymousQuestions));
  router.get('/anonymousComments', asyncHandler(getUserAnonymousComments));

  const escapeRegExp = function (string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
  }

  router.get('/searchUsers', asyncHandler(async (req, res, next) => {
    const searchText = req.query.search;
    if ((typeof searchText != 'string') || (searchText.length > 50)) {
      return next(invalidInputError());
    }
    const friendIds = await FriendList.getFriendIds(req.user._id);
    const query = {
      _id: { $in: friendIds },
      shadowBanned: { $ne: true },
    };
    if (searchText.length > 0) {
      query.firstName = { $regex: escapeRegExp(searchText), $options: "i" };
    }
    const projection = {
      _id: 1,
      firstName: 1,
      picture: { $first: '$pictures' },
      'personality.mbti': 1,
      enneagram: 1,
      horoscope: { $cond: [ '$hideHoroscope', null, '$horoscope' ] },
      karma: 1,
    };
    const users = await User.find(query, projection).limit(6);
    return res.json({ users });
  }));

  router.get('/profilePreviews', asyncHandler(async (req, res, next) => {
    const userIds = req.query.userIds;
    if (userIds?.length > 25) {
      return next(invalidInputError());
    }

    const query = {
      _id: { $in: userIds },
    };
    const projection = {
      _id: 1,
      firstName: 1,
      picture: { $first: '$pictures' },
      'personality.mbti': 1,
      enneagram: 1,
      horoscope: 1,
      karma: 1,
    };
    const users = await User.find(query, projection);
    return res.json({ users });
  }));

  router.put('/hideOnSocial', asyncHandler(async (req, res, next) => {
    if (req.user._id == req.body.user) {
      // don't let user hide themselves
      return res.json({});
    }

    const doc = await HideOnSocial.findOne({ from: req.user._id, to: req.body.user });
    if (!doc) {
      await HideOnSocial.create({ from: req.user._id, to: req.body.user });
    }
    return res.json({});
  }));

  router.delete('/hideOnSocial', asyncHandler(async (req, res, next) => {
    await HideOnSocial.deleteOne({ from: req.user._id, to: req.body.user });
    return res.json({});
  }));

  router.get('/hideOnSocial', asyncHandler(async (req, res, next) => {
    const matchBy = {
      from: req.uid,
    };
    if (req.query.beforeDate) {
      matchBy.createdAt = { $lt: new Date(req.query.beforeDate) };
    }

    const pageSize = constants.getPageSize();
    const users = [];

    while (!users.length) {
      const docs = await HideOnSocial.aggregate([
        { $match: matchBy },
        { $sort: { createdAt: -1 } },
        { $limit: pageSize },
        {
          $lookup: {
            from: 'users',
            let: { to: '$to' },
            pipeline: [
              { $match: { $expr: { $eq: ['$_id', '$$to'] } } },
              getBlockLookup('$_id', req.uid),
              {
                $match: {
                  shadowBanned: { $ne: true },
                  'block.0': { $exists: false },
                  _id: { $ne: req.uid },
                },
              },
              { $project: profilePreviewProjection },
            ],
            as: 'user',
          },
        },
        {
          $project: {
            createdAt: 1,
            user: 1,
          },
        },
      ]);

      if (!docs.length) break;

      for (const doc of docs) {
        if (Array.isArray(doc.user) && doc.user.length > 0) {
          doc.user = doc.user[0];
          users.push(doc);
        }
      }

      const lastCreatedAt = docs[docs.length - 1].createdAt;
      matchBy.createdAt = { $lt: lastCreatedAt };

      // break early if this was the last page
      if (docs.length < pageSize) break;
    }

    res.json({ users });
  }));

  router.get('/nearbyCities', asyncHandler(async (req, res, next) => {
    const user = req.user;
    if (!user.actualLocation) {
      return next(invalidInputError());
    }

    let nearbyCities = locationLib.getNearbyCities(user.actualLocation, user.locale);

    return res.json({
      nearbyCities,
    });
  }));

  router.put('/locationOverride', asyncHandler(async (req, res, next) => {
    const user = req.user;
    const city = req.body.city;
    const state = req.body.state;
    const countryCode = req.body.countryCode;
    if (!user.actualLocation || !city || !state || !countryCode) {
      return next(invalidInputError());
    }

    let nearbyCities = locationLib.getNearbyCities(user.actualLocation);
    let found = nearbyCities.find(x => x.city == city && x.state == state && x.countryCode == countryCode);
    if (!found) {
      return next(invalidInputError());
    }

    if (user.teleportLocation) {
      userLib.resetTeleport(user);
    }
    user.originalLocation = {
      city: user.actualCity,
      state: user.actualState,
      country: user.actualCountry,
      countryCode: user.actualCountryCode,
    };
    user.locationOverride = {
      city: city,
      state: state,
      country: found.country,
      countryCode: countryCode,
    };
    user.city = city;
    user.state = state;
    user.countryCode = countryCode;
    user.country = found.country;
    await user.save();

    return res.json({
      ...locationLib.getMyLocationFields(user),
    });
  }));

  router.delete('/locationOverride', asyncHandler(async (req, res, next) => {
    const user = req.user;
    if (user.teleportLocation) {
      userLib.resetTeleport(user);
    }
    user.originalLocation = undefined;
    user.locationOverride = undefined;
    user.countryCode = user.actualCountryCode;
    user.country = user.actualCountry;
    user.state = user.actualState;
    user.city = user.actualCity;
    await user.save();

    return res.json({
      ...locationLib.getMyLocationFields(user),
    });
  }));

  router.get('/post-purchase-screen', asyncHandler(async (req, res, next) => {
    const user = req.user;

    const purchaseInfo = await getPurchaseInfo(user);
    if (!purchaseInfo) {
      return next(notFoundError());
    }

    const profileViews = await getProfileViews(user, null, 'to');
    const chats = await chatLib.getApprovedChats(user);

    return res.json({
      baselineMetrics: {
        loveSent: 18.76,
        loveReceived: 49.2,
        DMSent: 0.48,
        DMReceived: 1.79,
        matches: 3.45,
        profileViews: 108.28,
      },
      userMetrics: {
        loveSent: user.metrics.numLikesSent,
        loveReceived: user.metrics.numLikesReceived,
        DMSent: user.metrics.numDMSent,
        DMReceived: user.metrics.numDMReceived,
        matches: user.metrics.numMatches,
        profileViews: user.metrics.numProfileViews,
      },
      purchaseInfo,
      matchPictures: chats.map(x => x.user.profilePicture).filter(x => x),
      profileViewPictures: profileViews.map(x => x.user.picture).filter(x => x),
    });
  }));

  router.get('/blocked', asyncHandler(async (req, res, next) => {
    const matchStage = { from: req.uid, hide: { $exists: false }, autoBlockedReason: { $exists: false } };
    if (req.query.beforeDate) {
      matchStage.createdAt = { $lt: new Date(req.query.beforeDate) };
    }

    const pageSize = constants.getPageSize();
    const users = [];

    while (!users.length) {
      const docs = await Block.aggregate([
        { $match: matchStage },
        { $sort: { createdAt: -1 } },
        { $limit: pageSize },
        {
          $lookup: {
            from: 'users',
            let: { to: '$to' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ['$_id', '$$to'] },
                      { $ne: ['$banned', true] },
                    ],
                  },
                },
              },
              {
                $project: profilePreviewProjection,
              },
            ],
            as: 'user',
          },
        },
        {
          $project: {
            createdAt: 1,
            user: 1,
          },
        },
      ]);

      if (!docs.length) break;

      for (const doc of docs) {
        if (Array.isArray(doc.user) && doc.user.length > 0) {
          doc.user = doc.user[0];
          users.push(doc);
        }
      }

      const lastCreatedAt = docs[docs.length - 1].createdAt;
      matchStage.createdAt = { $lt: lastCreatedAt };

      if (docs.length < pageSize) break;
    }

    res.json({ users });
  }));

  router.delete('/block', asyncHandler(findOtherUser), asyncHandler(async (req, res, next) => {
    const removed = await actionLib.unBlockUser(req.uid, req.body.user, true);
    if (removed) {
      const { otherUser } = req;
      if (otherUser.deviceId) {
        await HideList.updateOne(
          { userId: req.uid },
          { $pull: { deviceIds: otherUser.deviceId } },
        );
      }
      res.json({});

      // Unblock auto blocked users for Deviceid with this user
      await actionLib.unBlockAutoBlockedForDeviceId(req.uid, req.body.user);
    } else {
      next(invalidInputError('User not blocked'));
    }
  }));

  router.put('/sexuality', asyncHandler(async (req, res, next) => {
    const { user } = req;
    let { sexuality, sexualityVisibility } = req.body;

    if (sexuality === null && sexualityVisibility === null) {
      // User wants to delete sexuality and visibility
      user.sexuality = undefined;
      user.sexualityVisibility = undefined;
      user.sexualityPopupClosed = true;
      await user.save();
      return res.json({});
    }

    if (sexuality && !sexualityVisibility) {
      sexualityVisibility = 'sameSexuality';
    }

    const validSexualities = ['heterosexual', 'homosexual', 'bisexual', 'pansexual', 'asexual', 'other'];
    const validVisibility = ['all', 'lgbtq', 'sameSexuality', 'invisible'];

    if (!validSexualities.includes(sexuality) || !validVisibility.includes(sexualityVisibility)) {
      return next(invalidInputError());
    }

    user.sexuality = sexuality;
    user.sexualityVisibility = sexuality === 'heterosexual' ? 'all' : sexualityVisibility;

    await user.save();
    return res.json({});
  }));

  router.put('/closedSexualityPopup', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { sexualityPopup } = req.body;
    if (typeof sexualityPopup === 'boolean') {
      user.sexualityPopupClosed = sexualityPopup;
      await user.save();
      return res.json({});
    }

    return next(invalidInputError());
  }));

  router.post('/requestData', asyncHandler(async (req, res, next) => {
    const { user } = req;

    const coolOffPeriod = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentRequest = await DataRequestHistory.getLastRequest(user._id);

    if (recentRequest && (recentRequest.requestedAt > coolOffPeriod || recentRequest.status === 'pending')) {
      return next(forbiddenError(req.__('You have already submitted a request for your data. You will receive a download link via email.')));
    }

    const dataRequest = new DataRequestHistory({ userId: user._id });
    await dataRequest.save();

    res.status(200).json({});
  }));

  router.put('/open-notification', asyncHandler(async (req, res, next) => {
    const user = await User.findOne({ _id: req.uid });
    if(req.body.analyticsLabel){
      user.lastNotificationTopicsSent.set(req.body.analyticsLabel, null)
    }
    await user.save()

    return res.json({});
  }));

  router.put('/aiFilter', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { filter } = req.body;

    if (!filter || typeof filter !== 'string' || filter?.length > 300) {
      return next(invalidInputError());
    }

    const aiFilterPreference = await userLib.handleAiFilterPreference(user, filter.trim());
    if (aiFilterPreference === 'ERROR_TIMEOUT') {
      return next(requestTimeoutError(translate_frontend(`I'm sorry, I'm unable to process this right now, please try again.`, user.locale || 'en')));
    }
    if (aiFilterPreference === 'ERROR_TOO_LONG') {
      return next(invalidInputError(req.__('Please shorten your message and try again.')));
    }
    if (!aiFilterPreference) return next(applicationError());

    user.aiFilterPreference = aiFilterPreference._id;
    user.aiFilter = aiFilterPreference.filter;
    await user.save();

    return res.json({});
  }));

  router.delete('/aiFilter', asyncHandler(async (req, res, next) => {
    const { user } = req;
    if (user.aiFilterPreference) {
      await userLib.handleAiFilterCleanup(user.aiFilterPreference);
      user.aiFilterPreference = undefined;
      user.aiFilter = undefined;
      await user.save();
    }
    return res.json({});
  }));

  router.get('/popularAiFilters', asyncHandler(async (req, res, next) => {
    const { user } = req;

    const popularAiFilters = await userLib.getPopularAiFilters();
    return res.json({ popularAiFilters });
  }));

  /*
  router.get('/interestNames', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const interestNames = user?.interestNames || [];
    return res.json({ interestNames });
  }));
  */

  router.get('/unblocked', asyncHandler(async (req, res, next) => {
    const threeDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const beforeDate = req.query.beforeDate ? new Date(req.query.beforeDate) : null;

    // if beforeDate is older than or equal to 7 days ago
    if (beforeDate && beforeDate <= threeDaysAgo) {
      return res.json({ users: [] });
    }

    const matchStage = {
      from: req.uid,
      createdAt: {
        ...(beforeDate ? { $lt: beforeDate } : {}),
        $gte: threeDaysAgo,
      },
    };

    const data = await UnBlocked.aggregate([
      { $match: matchStage },
      { $sort: { createdAt: -1 } },
      { $limit: constants.getPageSize() },
      {
        $lookup: {
          from: 'users',
          let: { to: '$to' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$_id', '$$to'] },
                    { $ne: ['$banned', true] },
                  ],
                },
              },
            },
            { $project: profilePreviewProjection },
          ],
          as: 'user',
        },
      },
      { $match: { 'user.0': { $exists: true } } },
      { $unwind: '$user' },
      { $project: { createdAt: 1, user: 1 } },
    ]);

    return res.json({ users: data });
  }));

  router.patch('/visionVisibility', findUser, asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { hideFromVisionSearch } = req.body;

    if (typeof hideFromVisionSearch !== 'boolean') {
      return next(invalidInputError());
    }

    const userUpdate = hideFromVisionSearch
      ? { $set: { hideFromVisionSearch: true } }
      : { $unset: { hideFromVisionSearch: "" } };

    const embeddingsUpdate = hideFromVisionSearch
      ? { $set: { hideFromVisionSearch: true } }
      : { $unset: { hideFromVisionSearch: "" } };

    await User.findByIdAndUpdate(user._id, userUpdate);

    await UserEmbeddings.findOneAndUpdate(
      { user: user._id },
      embeddingsUpdate
    );

    res.json({});
  }));

  router.get('/infinityCopy', asyncHandler(async (req, res, next) => {
    const { user } = req;

    if (premiumLib.isPremium(user)) {
      return next(forbiddenError());
    }

    if (user.booInfinityCopy) {
      return res.json({ infinityCopy: user.booInfinityCopy });
    }

    // generate a new infinity copy
    const infinityCopy = await openai.generateInfinityCopy(user);
    if (!infinityCopy) {
      return next(applicationError());
    }

    user.booInfinityCopy = infinityCopy;
    await user.save();

    return res.json({ infinityCopy });
  }));

  router.put('/banAppeal', asyncHandler(async (req, res, next) => {
    const { user } = req;

    if (!user.shadowBanned || !user.banNotice || user.banNotice.appealStatus != 'allowed') {
      return next(forbiddenError());
    }

    await BanAppeal.create({
      user: user._id,
      comment: req.body.comment,
      bannedReasons: user.bannedReasons || [user.bannedReason],
      banNoticeReason: user.banNotice.reason,
    });

    user.banNotice.appealStatus = 'pending';
    user.banHistory.push({
      action: 'appealSubmitted',
      by: req.uid,
      date: Date.now(),
      notes: req.body.comment,
    });
    await user.save();

    return res.json({});
  }));

  router.put('/tempBanAppeal', asyncHandler(async (req, res, next) => {
    const { user } = req;

    console.log('<><><><><>tempBanAppeal', user.shadowBanned, user.profileTempBanReason, user.accountRestrictions?.profileTempBan?.appealStatus);
    if (!user.shadowBanned || !user.profileTempBanReason || user.accountRestrictions?.profileTempBan?.appealStatus != 'allowed') {
      return next(forbiddenError());
    }
    await ProfileTempBanAppeal.create({
      user: user._id,
      comment: req.body.comment,
      profileTempBanReason: user.profileTempBanReason,
      profileTempBanReportId: user.profileTempBanReportId,
      profileTempBanInfringingText: user.profileTempBanInfringingText,
      profileTempBanInfringingPictures: user.profileTempBanInfringingPictures,
    });

    user.accountRestrictions.profileTempBan.appealStatus = 'pending';

    user.banHistory.push({
      action: 'profileTempBanAppealSubmitted',
      by: req.uid,
      date: Date.now(),
      notes: req.body.comment,
    });
    await user.save();

    return res.json({});
  }));

  router.get('/isPurchaseAllowed', asyncHandler(async (req, res, next) => {
    const { user } = req;

    if (!user || !user.shadowBanned) {
      return res.json({
        isPurchaseAllowed: true,
      });
    }

    await reportLib.evaluateBanNotice(user);

    return res.json({
      isPurchaseAllowed: false,
      banNotice: userLib.formatBanNotice(user),
    });
  }));

  return router;
};
